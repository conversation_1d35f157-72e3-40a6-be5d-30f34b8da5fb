"use client";

import type React from "react";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Icons } from "@/components/icons";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { Project } from "@/types";

export function DashboardNav() {
  const path = usePathname();
  const projects: Project[] = [];
  const [open, setOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  const handleCreateProject = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsCreating(true);

    // Simulate project creation
    setTimeout(() => {
      setIsCreating(false);
      setOpen(false);
    }, 1000);
  };

  return (
    <>
      <div className="flex items-center justify-between px-6 py-2">
        <h2 className="text-lg font-semibold tracking-tight">Your Projects</h2>
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" size="icon" className="h-8 w-8">
              <Icons.add className="h-4 w-4" />
              <span className="sr-only">Create project</span>
            </Button>
          </DialogTrigger>
          <DialogContent>
            <form onSubmit={handleCreateProject}>
              <DialogHeader>
                <DialogTitle>Create new project</DialogTitle>
                <DialogDescription>
                  Add a new project to start analyzing contract data.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Project name</Label>
                  <Input id="name" placeholder="Enter project name" required />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="client">Client name</Label>
                  <Input id="client" placeholder="Enter client name" required />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="caseCode">Case Code</Label>
                  <Input
                    id="caseCode"
                    placeholder="e.g., 12102-25"
                    required
                    pattern="\d{5}-\d{2}"
                    title="Please enter a valid case code (e.g., 12102-25)"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Enter project description"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="submit" disabled={isCreating}>
                  {isCreating ? (
                    <>
                      <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    "Create project"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>
      <SidebarGroup>
        <SidebarGroupContent>
          <SidebarMenu>
            {projects.map((project) => (
              <SidebarMenuItem key={project.id}>
                <Link
                  href={`/project/${project.id}`}
                  className={`flex items-center gap-3 truncate ${
                    path === `/project/${project.id}` ? "bg-accent" : ""
                  }`}
                >
                  <Icons.fileText className="h-5 w-5 shrink-0" />
                  <span className="truncate">{project.name}</span>
                </Link>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    </>
  );
}
