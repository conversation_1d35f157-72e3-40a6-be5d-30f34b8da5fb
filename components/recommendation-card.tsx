import Link from "next/link"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import { Badge } from "@/components/ui/badge"

interface RecommendationCardProps {
  id: string
  title: string
  description: string
  currentState: string
  recommendation: string
  reasoning: string
  potentialSavings: string
  projectId: string
}

export function RecommendationCard({
  id,
  title,
  description,
  currentState,
  recommendation,
  reasoning,
  potentialSavings,
  projectId,
}: RecommendationCardProps) {
  return (
    <Card className="overflow-hidden border border-slate-200">
      <CardHeader className="bg-slate-50 pb-3">
        <CardTitle className="text-lg font-semibold">{title}</CardTitle>
        <CardDescription className="mt-1">{description}</CardDescription>
      </CardHeader>
      <CardContent className="pt-4 space-y-4">
        <div className="text-sm text-slate-600">
          <p className="text-slate-700 font-medium mb-1.5">Current State</p>
          {currentState}
        </div>

        <div className="bg-blue-50 rounded-md p-4 border border-blue-100">
          <p className="font-medium mb-1.5 text-blue-800">Recommended Action</p>
          <p className="text-sm text-blue-700">{recommendation}</p>
        </div>

        <div className="text-sm">
          <p className="text-slate-700 font-medium mb-1.5">Reasoning</p>
          <p className="text-slate-600">{reasoning}</p>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <p className="text-xs font-medium mb-1.5 text-slate-700">Potential Savings</p>
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 font-medium">
              {potentialSavings}
            </Badge>
          </div>
          <Link href={`/analysis/${id}?projectId=${projectId}&tab=recommendations`} passHref>
            <Button variant="outline" size="sm" className="border-slate-200 hover:bg-slate-50 hover:text-slate-900">
              <Icons.arrowRight className="mr-2 h-4 w-4" />
              Details
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}
