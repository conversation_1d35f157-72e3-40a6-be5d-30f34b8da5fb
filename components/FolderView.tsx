"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { ChevronDown, ChevronRight, Folder, File, X, CheckCircle2, AlertCircle } from 'lucide-react'
import { formatBytes } from '@/lib/utils'
import { FolderStructure } from '@/utils/file-upload'
import {getFileIcon} from "@/components/icons";
interface FolderViewProps {
  folderStructure: FolderStructure
  uploadProgress: Record<string, number>
  uploadStatus: Record<string, 'pending' | 'success' | 'error'>
  removeFile: (fileName: string) => void
  isProcessing: boolean
  level?: number
}



const getStatusIcon = (status: 'pending' | 'success' | 'error') => {
  switch (status) {
    case 'success':
      return <CheckCircle2 className="h-4 w-4 text-[#21BF61]" />
    case 'error':
      return <AlertCircle className="h-4 w-4 text-red-500" />
    default:
      return null
  }
}

export const FolderView: React.FC<FolderViewProps> = ({
  folderStructure,
  uploadProgress,
  uploadStatus,
  removeFile,
  isProcessing,
  level = -1,
}) => {
  const [isOpen, setIsOpen] = useState(folderStructure.isOpen || level === 0)
  const hasContent = folderStructure.files.length > 0 || folderStructure.folders.length > 0
  const isRoot = folderStructure.name === 'root'
  const paddingLeft = level * 10;

  const toggleFolder = () => {
    if (isProcessing) return
    setIsOpen(!isOpen)
  }

  return (
    <div className="folder-view">
      {!isRoot && hasContent && (
        <div
          className={`
            flex items-center py-0.5 px-1.5 rounded-md cursor-pointer text-[11px] mb-0.5
            ${isProcessing ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'}
          `}
          onClick={toggleFolder}
          style={{ paddingLeft: `${paddingLeft}px` }}
        >
          <div className="mr-0.5 text-gray-500">
            {isOpen ? <ChevronDown className="h-2.5 w-2.5" /> : <ChevronRight className="h-2.5 w-2.5" />}
          </div>
          <Folder className="h-3.5 w-3.5 mr-0.5 text-[#21BF61]/80" />
          <span className="font-medium leading-tight">{folderStructure.name}</span>
          <span className="ml-0.5 text-gray-500 leading-tight">
            ({folderStructure.files.length} file{folderStructure.files.length !== 1 ? 's' : ''})
          </span>
        </div>
      )}

      {(isRoot || isOpen) && (
        <div className={isRoot ? '' : 'ml-1'}>
          {folderStructure.folders.map((folder, index) => (
            <FolderView
              key={`${folder.path}-${index}`}
              folderStructure={folder}
              uploadProgress={uploadProgress}
              uploadStatus={uploadStatus}
              removeFile={removeFile}
              isProcessing={isProcessing}
              level={level + 1}
            />
          ))}
          {folderStructure.files.map((file, index) => (
            <div
              key={`${file.name}-${index}`}
              className={`
                relative overflow-hidden border border-gray-100 rounded-md transition-all duration-200 mb-1
                ${isProcessing ? 'opacity-50 cursor-not-allowed' : 'hover:border-[#21BF61]/20 hover:shadow-sm hover:shadow-[#21BF61]/5'}
              `}
              style={{ marginLeft: `${paddingLeft + (isRoot ? 0 : 12)}px` }}
            >
              <div className="p-1.5">
                <div className="flex items-center">
                  <div className="mr-1.5">{getFileIcon(file.name)}</div>
                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-center">
                      <div className="truncate pr-2">
                        <p className="text-[11px] font-medium truncate leading-tight">{file.name}</p>
                        <p className="text-[9px] text-gray-500 leading-tight">{formatBytes(file.size)}</p>
                      </div>
                      <div className="flex items-center">
                        {uploadStatus[file.name] && getStatusIcon(uploadStatus[file.name])}
                        <button
                          type="button"
                          disabled={isProcessing}
                          onClick={() => removeFile(file.name)}
                          className="ml-1 text-gray-400 hover:text-gray-600"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    </div>

                    <div className="w-full bg-gray-200 rounded-full h-1 mt-0.5 overflow-hidden">
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: `${uploadProgress[file.name] || 0}%` }}
                        transition={{ type: "spring", damping: 15 }}
                        className={`h-1 rounded-full ${
                          uploadStatus[file.name] === "error"
                            ? "bg-red-500"
                            : "bg-gradient-to-r from-[#21BF61] to-[#21BF61]/80"
                        }`}
                      />
                    </div>

                    {uploadStatus[file.name] === "error" && (
                      <p className="text-[9px] text-red-500 mt-0.5 leading-tight">Upload failed. Click to retry.</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default FolderView
