"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CheckCircle, AlertTriangle, XCircle, ChevronDown, ChevronRight, HelpCircle } from "lucide-react"
import { useState } from "react"

interface KeyElement {
  name: string
  status: "Aligned" | "Partially aligned" | "Not aligned" | "Not available"
  explanation: string
}

interface BenchmarkData {
  industryAverage: string
  peerComparison: string
  percentile: number
  keyElements?: KeyElement[]
}

interface BenchmarkingResultProps {
  id: string
  title: string
  description: string
  currentState: string
  benchmarkData: BenchmarkData
  projectId: string
}

export function BenchmarkingResult({
  id,
  title,
  description,
  currentState,
  benchmarkData,
  projectId,
}: BenchmarkingResultProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  // Function to get the appropriate icon for compliance status
  const getComplianceIcon = (status: string) => {
    const lowerStatus = status.toLowerCase()
    switch (lowerStatus) {
      case "aligned":
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case "partially aligned":
        return <AlertTriangle className="h-5 w-5 text-amber-500" />
      case "not aligned":
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <HelpCircle className="h-5 w-5 text-slate-400" />

    }
  }

  // Function to get the appropriate background color for compliance status
  const getComplianceBackground = (status: string) => {
    const lowerStatus = status.toLowerCase()
    switch (lowerStatus) {
      case "aligned":
        return "bg-green-50 border-green-100"
      case "partially aligned":
        return "bg-amber-50 border-amber-100"
      case "not aligned":
        return "bg-red-50 border-red-100"
      default:
        return "bg-slate-50 border-slate-200"
    }
  }

  // Function to sort key elements by compliance status
  const sortKeyElements = (elements: KeyElement[]) => {
    const statusPriority = {
      "Aligned": 1,
      "Partially aligned": 2,
      "Not aligned": 3,
      "Not available": 4, // Not Available items should appear last
    }

    return [...elements].sort((a, b) => {
      return statusPriority[a.status] - statusPriority[b.status]
    })
  }

  // Prepare key elements based on whether we're showing perfect compliance
  let processedKeyElements: KeyElement[] = []


  // Use the provided data with some "Not Available" items added
  processedKeyElements = benchmarkData.keyElements ? [...benchmarkData.keyElements] : []



  // Sort key elements
  const sortedKeyElements = sortKeyElements(processedKeyElements)

  // Calculate compliance counts
  const complianceCounts = {
    "Aligned": 0,
    "Partially aligned": 0,
    "Not aligned": 0,
    "Not available": 0,
  }

  if (sortedKeyElements.length > 0) {
    sortedKeyElements.forEach((element) => {
      complianceCounts[element.status]++
    })
  }

  // Calculate total elements for percentage calculation (excluding Not Available)
  const totalElements = sortedKeyElements.length

  // Calculate overall score
  // ALigned = 100%, Partially Aligned = 50%, Not Aligned = 0%
  const scorePoints = complianceCounts["Aligned"] + complianceCounts["Partially aligned"] * 0.5
  const maxPossiblePoints = totalElements - complianceCounts["Not available"]
  const overallScore = maxPossiblePoints > 0 ? Math.round((scorePoints / maxPossiblePoints) * 100) : 0


  return (
    <Card className={`overflow-hidden border border-slate-200"}`}>
      <CardHeader className={`pb-3 bg-slate-50"}`}>
        <CardTitle className="text-lg font-semibold">
          {title}
        </CardTitle>
        <CardDescription className="mt-1">{description}</CardDescription>
      </CardHeader>
      <CardContent className="pt-4 space-y-4">
        <div className="text-sm">
          <Alert
            variant="default"
            className={`border bg-slate-50 border-slate-200 text-slate-700"
              }`}
          >
            <AlertDescription>
              {currentState || benchmarkSummary}
            </AlertDescription>
          </Alert>
        </div>

        {/* Compliance Score Section */}
        {sortedKeyElements.length > 0 && (
          <div className="space-y-3">
            <p className="text-slate-700 font-medium">Alignment with Best Practises</p>

            {/* Score Display */}
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-baseline">
                <span className="text-3xl font-bold text-green-500">{overallScore}%</span>
              </div>
              <div className="text-sm text-slate-500">
                {complianceCounts["Not Available"] > 0 && (
                  <span>{complianceCounts["Not Available"]} elements not available for scoring</span>
                )}
              </div>
            </div>

            {/* Progress Bar */}
            <div className="w-full h-3 bg-slate-100 rounded-full overflow-hidden border border-slate-200">
              <div
                className="h-full bg-green-400 transition-all duration-500"
                style={{ width: `${overallScore}%` }}
              ></div>
            </div>

            {/* Goal indicator */}
            <div className="flex justify-end">
              <span className="text-xs text-slate-500">Goal: 100%</span>
            </div>

            {/* Score Breakdown */}
            <div className="grid grid-cols-3 gap-2 mt-3">
              <div className="bg-green-50 p-2 rounded-md text-center border border-green-100">
                <div className="flex justify-center mb-1">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                </div>
                <p className="text-xs font-medium text-green-700">Aligned with best practices</p>
                <p className="text-lg font-bold text-green-700">{complianceCounts["Aligned"]}</p>
              </div>
              <div className="bg-amber-50 p-2 rounded-md text-center border border-amber-100">
                <div className="flex justify-center mb-1">
                  <AlertTriangle className="h-4 w-4 text-amber-500" />
                </div>
                <p className="text-xs font-medium text-amber-700">Partially aligned with best practices</p>
                <p className="text-lg font-bold text-amber-700">{complianceCounts["Partially aligned"]}</p>
              </div>
              <div className="bg-red-50 p-2 rounded-md text-center border border-red-100">
                <div className="flex justify-center mb-1">
                  <XCircle className="h-4 w-4 text-red-500" />
                </div>
                <p className="text-xs font-medium text-red-700">Not aligned with best practices</p>
                <p className="text-lg font-bold text-red-700">{complianceCounts["Not aligned"]}</p>
              </div>
            </div>
          </div>
        )}

        {/* Detailed Benchmarking Section */}
        {sortedKeyElements.length > 0 && (
          <div className="space-y-3">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center justify-between w-full border-slate-200 hover:bg-slate-50 hover:text-slate-900"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              <div className="flex items-center">
                <Icons.barChart className="mr-2 h-4 w-4 text-slate-600" />
                Detailed Benchmarking
              </div>
              {isExpanded ? (
                <ChevronDown className="h-4 w-4 ml-2 text-slate-600" />
              ) : (
                <ChevronRight className="h-4 w-4 ml-2 text-slate-600" />
              )}
            </Button>

            {isExpanded && (
              <div className="space-y-2 transition-all duration-300 ease-in-out">
                {sortedKeyElements.map((element, index) => (
                  <div key={index} className={`p-3 rounded-md border ${getComplianceBackground(element.status)}`}>
                    <div className="flex items-start gap-3">
                      <div className="mt-0.5">{getComplianceIcon(element.status)}</div>
                      <div className={element.status === "Not Available" ? "text-slate-500" : "text-slate-700"}>
                        <p className="font-medium">{element.name || "Unnamed Element"}</p>
                        <p className="text-sm text-slate-600 mt-1">{element.explanation || "No explanation provided."}</p>
                        <p className="text-xs font-medium mt-1.5">{element.status}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
