'use client';

import React from 'react';
import { useDraggable } from '@dnd-kit/core';
import { DragItem } from '@/utils/drag-drop-types';

interface DraggableItemProps {
  id: string;
  data: DragItem;
  children: React.ReactNode;
  disabled?: boolean;
  className?: string;
}

export const DraggableItem: React.FC<DraggableItemProps> = ({
  id,
  data,
  children,
  disabled = false,
  className = '',
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({
    id,
    data,
    disabled,
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`${className} ${isDragging ? 'opacity-50' : ''} ${disabled ? 'cursor-not-allowed' : 'cursor-move'}`}
      {...listeners}
      {...attributes}
    >
      {children}
    </div>
  );
};
