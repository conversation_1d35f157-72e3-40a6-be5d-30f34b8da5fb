import {
  Alert<PERSON>ircle,
  <PERSON>R<PERSON>,
  ArrowLeft,
  Check,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  ChevronUp,
  Command,
  CreditCard,
  File,
  FileText,
  HelpCircle,
  ImageIcon,
  Laptop,
  Loader2,
  Moon,
  MoreVertical,
  Pizza,
  Plus,
  Settings,
  SunMedium,
  Trash,
  Twitter,
  User,
  X,
  MessageSquare,
  Send,
  Upload,
  BarChart,
  Lightbulb,
  FolderPlus,
  Layers,
  Download,
  Database,
  FileCodeIcon as FileContract,
  Tag,
  Calendar,
  Share,
  Maximize2,
  Minimize2,
  type LucideIcon,
} from "lucide-react"
import {EXTENSION_META_MAP, FileExtension} from "@/src/config/documentExtensions";
import React from "react";

export type Icon = LucideIcon

export const Icons = {
  logo: Command,
  close: X,
  spinner: Loader2,
  chevronLeft: ChevronLeft,
  chevronRight: ChevronRight,
  chevronDown: ChevronDown,
  chevronUp: ChevronUp,
  trash: Trash,
  post: FileText,
  page: File,
  media: ImageIcon,
  settings: Settings,
  billing: CreditCard,
  ellipsis: MoreVertical,
  add: Plus,
  warning: AlertCircle,
  user: User,
  arrowRight: ArrowRight,
  arrowLeft: ArrowLeft,
  help: HelpCircle,
  pizza: Pizza,
  sun: SunMedium,
  moon: Moon,
  laptop: Laptop,
  fileText: FileText,
  twitter: Twitter,
  check: Check,
  messageSquare: MessageSquare,
  send: Send,
  upload: Upload,
  barChart: BarChart,
  lightbulb: Lightbulb,
  folderPlus: FolderPlus,
  layers: Layers,
  download: Download,
  database: Database,
  fileContract: FileContract,
  tag: Tag,
  calendar: Calendar,
  share: Share,
  maximize: Maximize2,
  minimize: Minimize2,
}

export const getFileIcon = (fileName?: string) => {
  // extract extension including the dot
  const ext = fileName?.slice(fileName.lastIndexOf('.')).toLowerCase() as FileExtension | undefined

  const meta = ext && EXTENSION_META_MAP.get(ext)
  const Icon = meta?.Icon || File
  const colorClass = meta?.colorClass || 'text-gray-500'

  return <Icon className={`h-5 w-5 ${colorClass}`} />
}