import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>riangle, XCircle, HelpCircle } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { components } from "@/api/api-types"

type BenchmarkStatistics = components["schemas"]["BenchmarkStatisticsDTO"]

interface BenchmarkingDashboardProps {
  complianceCounts: BenchmarkStatistics
}

export function BenchmarkingDashboard({ complianceCounts }: BenchmarkingDashboardProps) {
  const compliant = complianceCounts.compliant;
  const partiallyCompliant = complianceCounts.partially_compliant;
  const nonCompliant = complianceCounts.non_compliant;
  const notAvailable = complianceCounts.not_available;

  const total = compliant + partiallyCompliant + nonCompliant + notAvailable
  const evaluatedTotal = compliant + partiallyCompliant + nonCompliant

  // Calculate percentages for evaluated items only
  const compliantPercent = Math.round((compliant / evaluatedTotal) * 100) || 0
  const partiallyCompliantPercent = Math.round((partiallyCompliant / evaluatedTotal) * 100) || 0
  const nonCompliantPercent = Math.round((nonCompliant / evaluatedTotal) * 100) || 0

  // Calculate overall compliance score (compliant = 100%, partially = 50%, non = 0%)
  const overallScore =
    Math.round(((compliant * 1 + partiallyCompliant * 0.5 + nonCompliant * 0) / evaluatedTotal) * 100) || 0

  // Calculate stroke dash values for donut chart
  const radius = 50
  const circumference = 2 * Math.PI * radius

  const compliantDash = (compliantPercent / 100) * circumference
  const partiallyCompliantDash = (partiallyCompliantPercent / 100) * circumference
  const nonCompliantDash = (nonCompliantPercent / 100) * circumference

  // Dummy data for evaluation dimensions (replace with actual data)
  const evaluationDimensions = ["Price", "Delivery", "Quality"]

  return (
    <Card className="mb-6">
      <CardContent className="p-6">
        <div className="flex items-center mb-4">
          <div className="bg-slate-100 rounded-full p-3 mr-4">
            <BarChart className="h-6 w-6 text-slate-700" />
          </div>
          <div>
            <h3 className="text-lg font-semibold">Benchmarking Summary</h3>
            <p className="text-sm text-slate-600">Contract compliance analysis across key dimensions</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Donut Chart */}
          <div className="flex justify-center items-center">
            <div className="relative w-64 h-64">
              <svg viewBox="0 0 120 120" className="w-full h-full -rotate-90">
                {/* Background circle */}
                <circle cx="60" cy="60" r="50" fill="none" stroke="#e2e8f0" strokeWidth="12" />

                {/* Compliant segment (green) */}
                {compliantPercent > 0 && (
                  <circle
                    cx="60"
                    cy="60"
                    r="50"
                    fill="none"
                    stroke="#22c55e"
                    strokeWidth="12"
                    strokeDasharray={`${compliantDash} ${circumference}`}
                    strokeDashoffset="0"
                    strokeLinecap="butt"
                  />
                )}

                {/* Partially Compliant segment (amber) */}
                {partiallyCompliantPercent > 0 && (
                  <circle
                    cx="60"
                    cy="60"
                    r="50"
                    fill="none"
                    stroke="#f59e0b"
                    strokeWidth="12"
                    strokeDasharray={`${partiallyCompliantDash} ${circumference}`}
                    strokeDashoffset={`${-compliantDash}`}
                    strokeLinecap="butt"
                  />
                )}

                {/* Non-Compliant segment (red) */}
                {nonCompliantPercent > 0 && (
                  <circle
                    cx="60"
                    cy="60"
                    r="50"
                    fill="none"
                    stroke="#ef4444"
                    strokeWidth="12"
                    strokeDasharray={`${nonCompliantDash} ${circumference}`}
                    strokeDashoffset={`${-(compliantDash + partiallyCompliantDash)}`}
                    strokeLinecap="butt"
                  />
                )}
              </svg>

              {/* Center text with overall score */}
              <div className="absolute inset-0 flex flex-col items-center justify-center">
                <span className="text-4xl font-bold">{overallScore}%</span>
                <span className="text-sm text-slate-600">Overall Score</span>
              </div>
            </div>
          </div>

          {/* Summary Cards */}
          <div className="col-span-1 lg:col-span-2 grid grid-cols-1 sm:grid-cols-2 gap-4">
            {/* Compliant Card */}
            <Card className="border-l-4 border-l-green-500">
              <CardContent className="p-4">
                <div className="flex items-center">
                  <div className="bg-green-100 p-2 rounded-full mr-3">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  </div>
                  <div>
                    <h3 className="font-medium">Aligned with best practices</h3>
                    <div className="flex items-baseline">
                      <span className="text-2xl font-bold mr-2">{compliant}</span>
                      <span className="text-sm text-slate-600">({compliantPercent}% of evaluated)</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Partially Compliant Card */}
            <Card className="border-l-4 border-l-amber-500">
              <CardContent className="p-4">
                <div className="flex items-center">
                  <div className="bg-amber-100 p-2 rounded-full mr-3">
                    <AlertTriangle className="h-5 w-5 text-amber-500" />
                  </div>
                  <div>
                    <h3 className="font-medium">Partially aligned with best practises</h3>
                    <div className="flex items-baseline">
                      <span className="text-2xl font-bold mr-2">{partiallyCompliant}</span>
                      <span className="text-sm text-slate-600">({partiallyCompliantPercent}% of evaluated)</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Non-Compliant Card */}
            <Card className="border-l-4 border-l-red-500">
              <CardContent className="p-4">
                <div className="flex items-center">
                  <div className="bg-red-100 p-2 rounded-full mr-3">
                    <XCircle className="h-5 w-5 text-red-500" />
                  </div>
                  <div>
                    <h3 className="font-medium">Not aligned with best practices</h3>
                    <div className="flex items-baseline">
                      <span className="text-2xl font-bold mr-2">{nonCompliant}</span>
                      <span className="text-sm text-slate-600">({nonCompliantPercent}% of evaluated)</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Not Available Card */}
            {notAvailable > 0 && (
              <Card className="border-l-4 border-l-slate-400">
                <CardContent className="p-4">
                  <div className="flex items-center">
                    <div className="bg-slate-100 p-2 rounded-full mr-3">
                      <HelpCircle className="h-5 w-5 text-slate-400" />
                    </div>
                    <div>
                      <h3 className="font-medium">Not Available</h3>
                      <div className="flex items-baseline">
                        <span className="text-2xl font-bold mr-2">{notAvailable}</span>
                        <span className="text-sm text-slate-600">
                          ({Math.round((notAvailable / total) * 100)}% of total)
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Additional Context */}
        <div className="mt-6 text-sm text-slate-600">
          <p>
            Analysis based on {total} key contract elements across {evaluationDimensions.length} dimensions.
            {notAvailable > 0 && ` ${notAvailable} elements could not be evaluated due to missing data.`}
          </p>
        </div>
      </CardContent>
    </Card>
  )
}

import { BarChart } from "lucide-react"
