'use client';

import React, { createContext, useContext } from 'react';
import {
  DndContext,
  DragOverlay,
  closestCenter,
} from '@dnd-kit/core';
import { useDragDrop } from '@/hooks/use-drag-drop';
import { Provider, UnmappedDocument } from '@/utils/drag-drop-types';
import { DragPreview } from './DragPreview';

interface DragDropContextType {
  isDragging: string | null;
  dragOverTarget: string | null;
  isAnyDragging: boolean;
  getDropPreview: (targetParams: any) => { canDrop: boolean; message: string };
  isItemDragging: (itemId: string) => boolean;
  isTargetDraggedOver: (targetId: string) => boolean;
  getDragState: () => any;
}

const DragDropContext = createContext<DragDropContextType | null>(null);

export const useDragDropContext = () => {
  const context = useContext(DragDropContext);
  if (!context) {
    return {
      isDragging: null,
      dragOverTarget: null,
      isAnyDragging: false,
      getDropPreview: () => ({ canDrop: false, message: "No item being dragged" }),
      isItemDragging: () => false,
      isTargetDraggedOver: () => false,
      getDragState: () => ({}),
    };
  }
  return context;
};

interface DragDropProviderProps {
  children: React.ReactNode;
  providers: Provider[];
  unmappedDocuments: UnmappedDocument[];
  onDataUpdate: (providers: Provider[], unmappedDocuments: UnmappedDocument[]) => void;
  onError?: (error: string) => void;
}

export const DragDropProvider: React.FC<DragDropProviderProps> = ({
  children,
  providers,
  unmappedDocuments,
  onDataUpdate,
  onError,
}) => {
  const {
    sensors,
    handleDragStart,
    handleDragOver,
    handleDragEnd,
    isDragging,
    dragOverTarget,
    isAnyDragging,
    draggedItemData,
    getDropPreview,
    isItemDragging,
    isTargetDraggedOver,
    getDragState,
  } = useDragDrop({
    providers,
    unmappedDocuments,
    onDataUpdate,
    onError,
  });

  const contextValue = {
    isDragging,
    dragOverTarget,
    isAnyDragging,
    getDropPreview,
    isItemDragging,
    isTargetDraggedOver,
    getDragState,
  };

  return (
    <DragDropContext.Provider value={contextValue}>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        {children}
        <DragOverlay>
          {(() => {
            const dragState = getDragState();
            return isDragging && draggedItemData && dragState.dragItem ? (
              <DragPreview
                dragItem={draggedItemData}
                dragType={dragState.dragItem.type}
              />
            ) : null;
          })()}
        </DragOverlay>
      </DndContext>
    </DragDropContext.Provider>
  );
};
