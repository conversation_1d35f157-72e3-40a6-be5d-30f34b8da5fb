import React from 'react';
import { useGetBenchmarkData } from '../api/benchmark';
import { components } from '../api/api-types';

type BenchmarkResultDTO = components["schemas"]["BenchmarkResultDTO"];

type BenchmarkDataProps = {
    projectId: string;
    contractId: string;
    transparencyFeatureId: string;
};

export const BenchmarkData: React.FC<BenchmarkDataProps> = ({
    projectId,
    contractId,
    transparencyFeatureId
}) => {
    const {
        data: benchmarkResults,
        isLoading,
        error
    } = useGetBenchmarkData(projectId, contractId, transparencyFeatureId);

    if (isLoading) {
        return <div>Loading benchmark data...</div>;
    }

    if (error) {
        return <div>Error loading benchmark data: {error.message}</div>;
    }

    if (!benchmarkResults || !benchmarkResults.benchmark_results || benchmarkResults.benchmark_results.length === 0) {
        return <div>No benchmark data available.</div>;
    }

    // Helper function to determine appropriate styling based on compliance status
    const getComplianceStatusStyle = (status: string) => {
        switch (status.toLowerCase()) {
            case 'compliant':
                return 'text-green-600';
            case 'non-compliant':
                return 'text-red-600';
            case 'partially compliant':
                return 'text-yellow-600';
            default:
                return 'text-gray-600';
        }
    };

    return (
        <div className="benchmark-data">
            <h2 className="text-xl font-bold mb-4">Benchmark Results</h2>
            <div className="space-y-4">
                {benchmarkResults.benchmark_results.map((result) => (
                    <div key={result.sub_feature_name} className="p-4 border rounded-md shadow-sm">
                        <h3 className="font-semibold text-lg">{result.sub_feature_name}</h3>
                        <div className="mt-2">
                            <span className="font-medium">Compliance Status: </span>
                            <span className={getComplianceStatusStyle(result.compliance_status)}>
                                {result.compliance_status}
                            </span>
                        </div>
                        <div className="mt-2">
                            <span className="font-medium">Reason: </span>
                            <p className="text-gray-700">{result.reason}</p>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};