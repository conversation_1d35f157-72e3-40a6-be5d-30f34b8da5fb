'use client';

import React from 'react';
import { File, MoveVertical, LockIcon, RefreshCw, ChevronRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { DragDropType } from '@/utils/drag-drop-types';

interface DragPreviewProps {
  dragItem: any;
  dragType: DragDropType;
}

export const DragPreview: React.FC<DragPreviewProps> = ({ dragItem, dragType }) => {
  if (!dragItem) return null;

  const baseClasses = "bg-white border border-gray-300 rounded-md shadow-lg opacity-90 max-w-xs";

  switch (dragType) {
    case DragDropType.Unmapped:
      return <UnmappedDocumentPreview item={dragItem} className={baseClasses} />;
    
    case DragDropType.Attachment:
      return <AttachmentPreview item={dragItem} className={baseClasses} />;
    
    case DragDropType.Contract:
      return <ContractPreview item={dragItem} className={baseClasses} />;
    
    default:
      return (
        <div className={baseClasses + " p-2"}>
          <div className="text-sm font-medium">Dragging...</div>
        </div>
      );
  }
};

interface PreviewItemProps {
  item: any;
  className: string;
}

const UnmappedDocumentPreview: React.FC<PreviewItemProps> = ({ item, className }) => {
  return (
    <div className={className + " p-3 flex items-center"}>
      <div className="mr-2 cursor-move text-gray-400">
        <MoveVertical className="h-4 w-4" />
      </div>
      <div className="mr-3">
        <File className="h-4 w-4 text-gray-500" />
      </div>
      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium truncate">{item.name}</div>
        <div className="text-xs text-gray-500">{item.type}</div>
      </div>
      <Badge className={`ml-2 ${
        item.moved
          ? 'bg-green-100 text-green-800 border-green-200'
          : 'bg-gray-100 text-gray-800'
      }`}>
        {item.moved ? 'Manual' : 'Unknown'}
      </Badge>
    </div>
  );
};

const AttachmentPreview: React.FC<PreviewItemProps> = ({ item, className }) => {
  const isLocked = item.is_existing && !item.is_replaced;
  const isReplaced = item.is_replaced;
  const isImmutable = isLocked || isReplaced;

  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 80) return 'bg-green-100 text-green-800 border-green-200';
    if (confidence >= 60) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  return (
    <div className={className + " p-3 flex items-center"}>
      <div className={`mr-2 ${isImmutable ? "text-gray-300" : "cursor-move text-gray-400"}`}>
        {isReplaced ? (
          <RefreshCw className="h-4 w-4 text-orange-500 opacity-70" />
        ) : isLocked ? (
          <LockIcon className="h-4 w-4 opacity-70" />
        ) : (
          <MoveVertical className="h-4 w-4" />
        )}
      </div>
      <div className="mr-3">
        <File className={`h-4 w-4 text-gray-500 ${isImmutable ? "opacity-70" : ""}`} />
      </div>
      <div className="flex-1 min-w-0">
        <div className={`text-sm ${isImmutable ? "text-gray-500 font-normal opacity-85" : "font-medium"} truncate`}>
          {item.name}
        </div>
        <div className="text-xs text-gray-500">
          {item.type}
          {isReplaced && <span className="ml-2 text-xs text-orange-400">(Updated)</span>}
        </div>
        {item.contractName && (
          <div className="text-xs text-blue-600 truncate">
            From: {item.contractName}
          </div>
        )}
      </div>
      <Badge className={`ml-2 ${
        isReplaced
          ? 'bg-orange-100 text-orange-700 border-orange-200'
          : isLocked
            ? 'bg-gray-100 text-gray-600 border-gray-200'
            : item.moved
              ? 'bg-green-100 text-green-800 border-green-200'
              : getConfidenceBadge(item.confidence)
      }`}>
        {isReplaced
          ? 'Updated'
          : isLocked
            ? 'Existing'
            : item.moved
              ? 'Manual'
              : `${item.confidence}%`}
      </Badge>
    </div>
  );
};

const ContractPreview: React.FC<PreviewItemProps> = ({ item, className }) => {
  const isLocked = item.is_existing && !item.is_replaced;
  const isReplaced = item.is_replaced;

  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 80) return 'bg-green-100 text-green-800 border-green-200';
    if (confidence >= 60) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  return (
    <div className={className + " p-4"}>
      <div className="flex items-center">
        <div className="mr-2">
          <ChevronRight className={`h-5 w-5 ${
            isLocked ? 'text-gray-400' : 'text-gray-600'
          }`} />
        </div>
        <div className="flex-1 min-w-0">
          <div className={`text-sm ${isLocked ? "text-gray-500 font-normal opacity-85" : "font-medium"} truncate`}>
            {item.name}
          </div>
          <div className="text-xs text-gray-500 flex items-center gap-2">
            <span>{item.contractType}</span>
            {item.providerName && (
              <>
                <span>•</span>
                <span className="text-blue-600">{item.providerName}</span>
              </>
            )}
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge className={`${
            isReplaced
              ? 'bg-orange-100 text-orange-700 border-orange-200'
              : isLocked
                ? 'bg-gray-100 text-gray-600 border-gray-200'
                : item.moved
                  ? 'bg-green-100 text-green-800 border-green-200'
                  : getConfidenceBadge(item.confidence)
          }`}>
            {isReplaced
              ? 'Updated'
              : isLocked
                ? 'Existing'
                : item.moved
                  ? 'Manual'
                  : `${item.confidence}%`}
          </Badge>
        </div>
      </div>
    </div>
  );
};
