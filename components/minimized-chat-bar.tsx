"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Icons } from "@/components/icons";

interface MinimizedChatBarProps {
  onExpand: () => void;
  onClose: () => void;
  title?: string;
}

export function MinimizedChatBar({
  onExpand,
  onClose,
  title = "Contract Assistant",
}: MinimizedChatBarProps) {
  return (
    <div className="flex items-center justify-between bg-white rounded-tl-lg shadow-lg px-4 py-3 w-full h-12 hover:bg-gray-50 transition-colors cursor-pointer animate-subtle-bounce" onClick={onExpand}>
      <div className="flex items-center gap-3">
        <div className="bg-primary/10 rounded-full p-1.5 flex items-center justify-center">
          <Icons.messageSquare className="h-4 w-4 text-primary" />
        </div>
        <span className="text-lg font-semibold truncate">{title}</span>
      </div>
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" onClick={onExpand} className="h-8 w-8 hover:bg-gray-100">
          <Icons.chevronUp className="h-4 w-4" />
          <span className="sr-only">Expand</span>
        </Button>
        <Button variant="ghost" size="icon" onClick={onClose} className="h-8 w-8 hover:bg-gray-100">
          <Icons.close className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </Button>
      </div>
    </div>
  );
}
