import { useGetBenchmarkData } from "@/api/benchmark";

// Create a new component to handle benchmark data
function BenchmarkFeatureCard({ feature, projectId, contractId }) {
    const { data, isLoading, isError } = useGetBenchmarkData(
        projectId,
        contractId,
        feature.id
    );

    if (isLoading) {
        return (
            <BenchmarkingResult
                key={feature.id}
                id={feature.id}
                title={feature.feature_name}
                description={feature.description || "No description available"}
                currentState="Loading benchmark data..."
                benchmarkData={{
                    industryAverage: "Loading...",
                    peerComparison: "Loading...",
                    percentile: 0,
                }}
                projectId={projectId}
            />
        );
    }

    if (isError) {
        return (
            <BenchmarkingResult
                key={feature.id}
                id={feature.id}
                title={feature.feature_name}
                description={feature.description || "No description available"}
                currentState="Error loading benchmark data. Please try again."
                benchmarkData={{
                    industryAverage: "Error loading data",
                    peerComparison: "Error loading data",
                    percentile: 0,
                }}
                projectId={projectId}
            />
        );
    }

    // Transform the benchmark results into keyElements for the benchmarkData
    const keyElements = data?.benchmark_results?.map(result => ({
        name: result.sub_feature_name,
        status: result.compliance_status,
        explanation: result.reason
    })) || [];

    // Create benchmark data from the results
    const benchmarkData = {
        industryAverage: "Industry standard based on collected data",
        peerComparison: "Comparison with similar organizations",
        percentile: 50, // Default percentile
        keyElements: keyElements
    };

    const summary = data?.summary || "No summary available";

    return (
        <BenchmarkingResult
            key={feature.id}
            id={feature.id}
            title={feature.feature_name}
            description={feature.description || "No description available"}
            currentState={summary}
            benchmarkData={benchmarkData}
            projectId={projectId}
        />
    );
}