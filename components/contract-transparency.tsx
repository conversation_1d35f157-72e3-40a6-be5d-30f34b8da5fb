"use client"

import { useEffect, useState } from "react"
import MarkdownPreview from "@uiw/react-markdown-preview";
import { Icons } from "@/components/icons"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Button } from "@/components/ui/button"
import { Edit, Trash2, ChevronDown, ChevronRight, AlertCircle, RefreshCw } from "lucide-react"
import { useRegenerateTransparencyFeatureData } from "@/api/transparency-features"
import { useParams } from "next/navigation"

interface Source {
  text: string
  metadata: {
    fileName: string
    page: number
    section: string
  }
}

interface SubFeature {
  title: string
  content?: string
  sources?: Source[]
  dataAvailable?: boolean
}

interface ContractTransparencyProps {
  id: string
  title: string
  description: string
  summary: string
  sources: Source[]
  subFeatures?: SubFeature[]
  isCustom?: boolean
  feature: any
  onEdit?: (featureId: string) => void
  onDelete?: (featureId: string) => void
  isLoading?: boolean
  isError?: boolean
  isDeleting: boolean
  refreshTriggered: number
}

export function ContractTransparency({
  id,
  title,
  description,
  summary,
  sources,
  feature,
  subFeatures = [],
  isCustom = false,
  onEdit,
  onDelete,
  isLoading = false,
  isError = false,
  isDeleting = false,
  refreshTriggered,
}: ContractTransparencyProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  // Count how many sub-features have data available
  const params = useParams();
  const availableSubFeatures = subFeatures.filter((feature) => feature.dataAvailable !== false).length
  const totalSubFeatures = subFeatures.length
  const { mutate: regenerate, isPending: isRefreshing } = useRegenerateTransparencyFeatureData(params.project_id as string,params.contract_id as string, feature?.id ?? "");

  const handleRefresh = async () => {
      regenerate();
  }

  useEffect(() => {
      regenerate();
  }, [refreshTriggered, regenerate]);

  // Add loading state handling
  if (isLoading) {
    return (
      <Card className="overflow-hidden border border-slate-200">
        <CardHeader className="pb-3 bg-slate-50">
          <CardTitle className="text-lg font-semibold">{title}</CardTitle>
          <CardDescription className="mt-1">{description}</CardDescription>
        </CardHeader>
        <CardContent className="pt-4 space-y-4">
          <div className="flex items-center justify-center p-4">
            <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            <span>Loading contract data...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Add error state handling
  if (isError) {
    return (
      <Card className="overflow-hidden border border-red-100">
        <CardHeader className="pb-3 bg-red-50">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-lg font-semibold">{title}</CardTitle>
              <CardDescription className="mt-1">{description}</CardDescription>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleRefresh}
              className="h-8 w-8 text-slate-600 hover:text-slate-900"
            >
              {isRefreshing ? (
                <Icons.spinner className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
              <span className="sr-only">Refresh</span>
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-4 space-y-4">
          <div className="flex items-center text-red-600">
            <AlertCircle className="mr-2 h-4 w-4" />
            <span>Error loading data. Please try again.</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={`overflow-hidden border ${isCustom ? "border-blue-200" : "border-slate-200"}`}>
      <CardHeader className={`pb-3 ${isCustom ? "bg-blue-50" : "bg-slate-50"}`}>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold">{title}</CardTitle>
            <CardDescription className="mt-1">{description}</CardDescription>
          </div>
          <div className="flex space-x-1">
            {isCustom && (
              <>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => onEdit ? onEdit(feature) : undefined}
                  disabled={isDeleting}
                  className="h-8 w-8 text-slate-600 hover:text-slate-900"
                >
                  <Edit className="h-4 w-4" />
                  <span className="sr-only">Edit</span>
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => onDelete ? onDelete(feature.id) : undefined}
                  className="h-8 w-8 text-slate-600 hover:text-slate-900"
                >
                  {isDeleting ? (
                    <>
                      <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                      <span className="sr-only">Deleting</span>
                    </>
                  ) : (
                    <>
                      <Trash2 className="h-4 w-4" />
                      <span className="sr-only">Delete</span>
                    </>
                  )}
                </Button>
              </>
            )}
            <Button
              variant="ghost"
              size="icon"
              onClick={handleRefresh}
              className="h-8 w-8 text-slate-600 hover:text-slate-900"
            >
              {isRefreshing ? (
                <Icons.spinner className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
              <span className="sr-only">Refresh</span>
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-4 space-y-4 shadow-sm">
        <MarkdownPreview
          source={summary}
          style={{
            fontSize: '0.875rem',
            lineHeight: '1.25rem',
            color: isCustom ? '#334155' : '#475569' // Should be same as text-slate-700 and text-slate-600
          }}
        />

        {/* Display sources for the summary if no sub-features exist */}
        {sources.length > 0 && subFeatures.length === 0 && (
          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value="sources" className="border-slate-200">
              <AccordionTrigger className="text-sm font-medium text-slate-700 hover:text-slate-900 hover:no-underline py-2">
                Source References
              </AccordionTrigger>
              <AccordionContent>
                <ul className="space-y-3 text-sm">
                  {sources.map((source, index) => (
                    <li key={index} className="border-l-2 border-slate-200 pl-3">
                      <p className="italic mb-1 text-slate-700">"{source.text}"</p>
                      <p className="text-xs text-slate-500">
                        Source: {source.metadata.fileName}, Page: {source.metadata.page}, Section:{" "}
                        {source.metadata.section}
                      </p>
                    </li>
                  ))}
                </ul>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        )}

        {subFeatures.length > 0 && (
          <div className="mt-4">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center justify-between w-full border-slate-200 hover:bg-slate-50 hover:text-slate-900"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              <div className="flex items-center">
                <Icons.fileText className="mr-2 h-4 w-4 text-slate-600" />
                Key Contract Elements
                {availableSubFeatures < totalSubFeatures && (
                  <span className="ml-2 text-xs text-amber-600 bg-amber-50 px-2 py-0.5 rounded-full">
                    {availableSubFeatures}/{totalSubFeatures} available
                  </span>
                )}
              </div>
              {isExpanded ? (
                <ChevronDown className="h-4 w-4 ml-2 text-slate-600" />
              ) : (
                <ChevronRight className="h-4 w-4 ml-2 text-slate-600" />
              )}
            </Button>

            {isExpanded && (
              <div className="space-y-3 mt-3 transition-all duration-300 ease-in-out">
                {subFeatures.map((feature, index) => (
                  <div
                    key={index}
                    className={`rounded-md p-3 border ${feature.dataAvailable === false ? "bg-slate-50 border-slate-200" : "bg-white border-slate-200"
                      }`}
                  >
                    <h2 className="text-lg font-bold text-slate-800 border-b border-slate-300 pb-1 mb-2">
                      {feature.title}
                    </h2>
                      {feature.dataAvailable === false && (
                        <span className="ml-2 text-xs text-slate-500 flex items-center">
                          <AlertCircle className="h-3 w-3 mr-1" />
                          Data not available
                        </span>
                      )}

                    {feature.dataAvailable === false ? (
                      <p className="text-sm text-slate-400 italic">No information found in the provided documents.</p>
                    ) : (
                      <>
                        {feature.content}

                        {/* Display sources for each sub-feature */}
                        {(feature.sources?.length > 0 || (index === 0 && sources.length > 0)) && (
                          <Accordion type="single" collapsible className="w-full mt-2">
                            <AccordionItem value={`sources-${index}`} className="border-slate-200">
                              <AccordionTrigger className="text-xs font-medium py-2 text-slate-600 hover:text-slate-900 hover:no-underline">
                                Source References
                              </AccordionTrigger>
                              <AccordionContent>
                                <ul className="space-y-2 text-xs">
                                  {/* Use feature sources if available, otherwise use the main sources for the first sub-feature */}
                                  {(feature.sources || (index === 0 ? sources : [])).map((source, idx) => (
                                    <li key={idx} className="border-l-2 border-slate-200 pl-2">
                                      <p className="italic mb-1 text-slate-600">"{source.text}"</p>
                                      <p className="text-xs text-slate-500">
                                        Source: {source.metadata.fileName}, Page: {source.metadata.page}, Section:{" "}
                                        {source.metadata.section}
                                      </p>
                                    </li>
                                  ))}
                                </ul>
                              </AccordionContent>
                            </AccordionItem>
                          </Accordion>
                        )}
                      </>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {isCustom && (
          <div className="text-xs text-slate-500 mt-2 pt-2 border-t border-slate-200">
            <span className="bg-blue-50 text-blue-600 px-2 py-1 rounded-md font-medium">Custom Feature</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
