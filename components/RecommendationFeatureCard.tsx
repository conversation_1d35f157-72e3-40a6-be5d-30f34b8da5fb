import { useGetRecommendationData } from "@/api/recommendations";
import { ContractTransparency } from "@/components/contract-transparency";
import MarkdownPreview from "@uiw/react-markdown-preview";

export function RecommendationFeatureCard({ feature, projectId, contractId }: { feature: any; projectId: string, contractId: string }) {
    const { data: recommendationResults, isLoading, isError } = useGetRecommendationData(projectId, contractId, feature.id);

    if (isLoading) {
        return (
            <ContractTransparency
                key={feature.id}
                id={feature.id}
                title={feature.feature_name || feature.title}
                description={feature.description || "No description available"}
                extractedData="Loading recommendations..."
                sources={[]}
                isLoading={true}
            />
        );
    }

    if (isError) {
        return (
            <ContractTransparency
                key={feature.id}
                id={feature.id}
                title={feature.feature_name || feature.title}
                description={feature.description || "No description available"}
                extractedData="Error loading recommendations. Please try again."
                sources={[]}
                isError={true}
            />
        );
    }

    const subFeatures = (recommendationResults?.recommendation_result ?? []).map((result) => ({
        title: result.sub_feature_name,
        content: (
            <div style={{ background: "none" }} className="markdown-wrapper">
                <MarkdownPreview source={result.recommendation} />
            </div>
        ),
        dataAvailable: true,
    }));

    const summary =
        (recommendationResults?.recommendation_result?.length ?? 0) > 0
            ? recommendationResults?.summary
            : "No recommendations available.";

    return (
        <ContractTransparency
            key={feature.id}
            id={feature.id}
            title={feature.feature_name || feature.title}
            description={feature.description || "No description available"}
            summary={summary}
            sources={[]}
            subFeatures={subFeatures}
        />
    );
}
