import { useGetBenchmarkData } from "@/api/benchmark";
import { BenchmarkingResult } from "@/components/benchmarking-result";
import Markdown from "react-markdown";
import ReactMarkdown from "react-markdown";
import MarkdownPreview from "@uiw/react-markdown-preview";

export function BenchmarkFeatureCard({ feature, projectId, contractId }: { feature: any; projectId: string, contractId: string }) {
    const { data, isLoading, isError } = useGetBenchmarkData(projectId, contractId, feature.id);

    if (isLoading) {
        return (
            <BenchmarkingResult
                key={feature.id}
                id={feature.id}
                title={feature.feature_name}
                description={feature.description || "No description available"}
                currentState="Loading benchmark data..."
                benchmarkData={{
                    industryAverage: "Loading...",
                    peerComparison: "Loading...",
                    percentile: 0,
                }}
                projectId={projectId}
            />
        );
    }

    if (isError) {
        return (
            <BenchmarkingResult
                key={feature.id}
                id={feature.id}
                title={feature.feature_name}
                description={feature.description || "No description available"}
                currentState="Error loading benchmark data. Please try again."
                benchmarkData={{
                    industryAverage: "Error loading data",
                    peerComparison: "Error loading data",
                    percentile: 0,
                }}
                projectId={projectId}
            />
        );
    }

    const getComplianceStatusWording = (status: string) => {
        switch (status.toLowerCase()) {
            case 'compliant':
                return 'Aligned';
            case 'non compliant':
                return 'Not aligned';
            case 'partially compliant':
                return 'Partially aligned';
            default:
                return 'Unknown';
        }
    };

    const keyElements = data?.benchmark_results?.map((result) => ({
        name: result.sub_feature_name,
        status: getComplianceStatusWording(result.compliance_status),
        explanation: (
            <div className="markdown-wrapper">
                <MarkdownPreview style={{ background: "none" }} source={result.reason} />
            </div>
        ),
    })) || [];

    const complianceStats = keyElements.reduce(
        (acc, element) => {
            const status = element.status;
            if (status === "Aligned") acc.compliant++;
            else if (status === "Partially aligned") acc.partiallyCompliant++;
            else if (status === "Not aligned") acc.nonCompliant++;
            else acc.notAvailable++;
            return acc;
        },
        { compliant: 0, partiallyCompliant: 0, nonCompliant: 0, notAvailable: 0 }
    );

    const scorePoints = complianceStats.compliant + complianceStats.partiallyCompliant * 0.5;
    const maxPossiblePoints = keyElements.length - complianceStats.notAvailable;
    const percentile = maxPossiblePoints > 0 ? Math.round((scorePoints / maxPossiblePoints) * 100) : 0;

    const benchmarkData = {
        industryAverage: feature.benchmarkData?.industryAverage || "Industry standard based on collected data",
        peerComparison: feature.benchmarkData?.peerComparison || "Comparison with similar organizations",
        percentile: percentile,
        keyElements: keyElements,
    };

    return (
        <BenchmarkingResult
            key={feature.id}
            id={feature.id}
            title={feature.feature_name || feature.title}
            description={feature.description || "No description available"}
            currentState={data?.summary || "No summary available"}
            benchmarkData={benchmarkData}
            projectId={projectId}
        />
    );
}
