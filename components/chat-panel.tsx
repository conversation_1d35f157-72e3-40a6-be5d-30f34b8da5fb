"use client";

import type React from "react";

import { useEffect, useMemo, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Icons } from "@/components/icons";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useConversation, useSendMessage } from "@/api/chat";
import MarkdownPreview from "@uiw/react-markdown-preview";

// Update the ChatPanelProps interface to include optional dimension-specific props
interface ChatPanelProps {
  projectId: string;
  contractId: string;
  onClose: () => void;
  onExpand?: () => void;
  onMinimize?: () => void;
  isExpanded?: boolean;
  isMinimized?: boolean;
  hideCloseButton?: boolean;
  dimensionTitle?: string;
  dimensionDescription?: string;
  extractedData?: string;
  recommendation?: string;
}

interface ChatMessage {
  id: string;
  content: string;
  role: "user" | "assistant";
  timestamp: Date;
}

// Update the ChatPanel component to handle dimension-specific context
export function ChatPanel({
  projectId,
  contractId,
  onClose,
  onExpand,
  onMinimize,
  isExpanded,
  isMinimized,
  hideCloseButton,
  dimensionTitle,
  dimensionDescription,
  extractedData,
  recommendation,
}: ChatPanelProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: "1",
      content: dimensionTitle
        ? `Hello! I'm your ${dimensionTitle} analysis assistant. How can I help you analyze this dimension?`
        : "Hello! I'm your contract analysis assistant. How can I help you analyze your contracts today?",
      role: "assistant",
      timestamp: new Date(),
    },
  ]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [currentMessage, setCurrentMessage] = useState<string | null>(null);
  const { data: conversation } = useConversation(projectId, contractId);
  const { mutate: sendMessage } = useSendMessage(projectId, contractId);

  const handleSendMessage = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!input.trim()) return;

    setCurrentMessage(input);
    setInput("");
    setIsLoading(true);
    sendMessage({
      question: input,
    });
  };

  useEffect(() => {
    setIsLoading(false);
  }, [conversation]);

  const mergedMessages = useMemo(() => {
    const msgs = conversation?.messages ?? [];

    if (isLoading && currentMessage) {
      return [
        ...(conversation?.messages ?? []),
        {
          id: "",
          content: currentMessage,
          author: "user",
          created_at: new Date().toISOString(),
        },
      ];
    }

    return msgs;
  }, [conversation?.messages, isLoading, currentMessage]);

  return (
    <div className={`flex h-full flex-col w-full ${isExpanded ? "pt-2" : ""}`}>
      <div className={`flex items-center justify-between border-b ${isExpanded ? "px-2" : "px-4"} py-2 h-12 ${isExpanded ? "mx-4" : ""}`}>
        <div className="flex items-center gap-3">
          <div className="bg-primary/10 rounded-full p-1.5 flex items-center justify-center">
            <Icons.messageSquare className="h-4 w-4 text-primary" />
          </div>
          <span className="text-lg font-semibold">
            {dimensionTitle
              ? `${dimensionTitle} Assistant`
              : "Contract Assistant"}
          </span>
        </div>
        <div className="flex items-center gap-1">
          {onExpand && !isExpanded && (
            <Button variant="ghost" size="icon" onClick={onExpand} className="h-8 w-8">
              <Icons.maximize className="h-4 w-4" />
              <span className="sr-only">Expand</span>
            </Button>
          )}
          {onMinimize && !isMinimized && (
            <Button variant="ghost" size="icon" onClick={onMinimize} className="h-8 w-8">
              <Icons.chevronDown className="h-4 w-4" />
              <span className="sr-only">Minimize</span>
            </Button>
          )}
          {!hideCloseButton && (
            <Button variant="ghost" size="icon" onClick={onClose} className="h-8 w-8">
              <Icons.close className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>
          )}
        </div>
      </div>

      {/* Add dimension context card if dimension props are provided */}
      {dimensionTitle && (
        <div className={`p-4 border-b ${isExpanded ? "px-4" : ""}`}>
          <div className="bg-[#F1EEEA] rounded-lg p-3 text-sm">
            <p className="font-medium mb-1">Context</p>
            {dimensionDescription && (
              <p className="mb-1 text-muted-foreground">
                {dimensionDescription}
              </p>
            )}
            {extractedData && (
              <p className="mb-1">
                <span className="font-medium">Current State:</span>{" "}
                {extractedData}
              </p>
            )}
            {recommendation && (
              <p>
                <span className="font-medium">Recommendation:</span>{" "}
                {recommendation}
              </p>
            )}
          </div>
        </div>
      )}

      <ScrollArea
        className={`flex-1 overflow-auto ${isExpanded ? "px-4 py-4" : "p-4"}`}
        style={{ maxHeight: isExpanded ? "calc(80vh - 140px)" : "calc(60vh - 110px)" }}>
        <div className="space-y-4">
          {mergedMessages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.author === "user" ? "justify-end" : "justify-start"
                }`}
            >
              <div
                className={`rounded-lg px-4 py-2 max-w-[80%] ${message.author === "user"
                  ? "bg-[#A8F0B8] text-foreground"
                  : "bg-muted"
                  }`}
              >
                <p>
                  <MarkdownPreview
                    source={message.content}
                    style={{
                      backgroundColor: "transparent",
                    }}
                  />
                </p>
                <p className="mt-1 text-xs opacity-70">
                  {new Date(message.created_at).toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </p>
              </div>
            </div>
          ))}
          {isLoading && (
            <div className="flex justify-start">
              <div className="rounded-lg bg-muted px-4 py-2">
                <Icons.spinner className="h-4 w-4 animate-spin" />
              </div>
            </div>
          )}
        </div>
      </ScrollArea>
      <div className={`border-t bg-white p-4`}>
        <form onSubmit={handleSendMessage} className="flex gap-2">
          <Input
            placeholder={
              dimensionTitle
                ? `Ask about ${dimensionTitle.toLowerCase()}...`
                : "Ask about your contracts..."
            }
            value={input}
            onChange={(e) => setInput(e.target.value)}
            disabled={isLoading}
          />
          <Button
            type="submit"
            size="icon"
            disabled={isLoading || !input.trim()}
          >
            <Icons.arrowRight className="h-4 w-4" />
            <span className="sr-only">Send</span>
          </Button>
        </form>
      </div>
    </div>
  );
}
