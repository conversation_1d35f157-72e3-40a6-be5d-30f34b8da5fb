import { useDeleteCustomTransparencyFeature, useGetTransparencyFeatureData, useRegenerateTransparencyFeatureData } from "@/api/transparency-features";
import { ContractTransparency } from "@/components/contract-transparency";
import MarkdownPreview from "@uiw/react-markdown-preview";
import "./contract-transparency.css";

export function TransparencyFeatureCard({
    feature,
    projectId,
    contractId,
    handleEditFeature,
    refreshTriggered,
    isCustom = false
}: {
    feature: any;
    projectId: string;
    contractId: string;
    handleEditFeature: (feature: any) => void;
    refreshTriggered: number;
    isCustom?: boolean;
}) {
    const { data, isFetching, isError } = useGetTransparencyFeatureData(projectId, contractId, feature.id);
    const {
        mutate: deleteCustomFeature,
        isPending: isDeletingFeature,
    } = useDeleteCustomTransparencyFeature(projectId, feature.id);

    if (isFetching) {
        return (
            <ContractTransparency
                key={feature.id}
                id={feature.id}
                title={feature.feature_name}
                description={feature.description || "No description available"}
                extractedData="Loading data..."
                sources={[]}
                isCustom={isCustom}
                isLoading={true}
                refreshTriggered={refreshTriggered}
                onDelete={deleteCustomFeature}
                isDeleting={isDeletingFeature}
                onEdit={handleEditFeature}
            />
        );
    }

    if (isError || !data) {
        return (
            <ContractTransparency
                key={feature.id}
                id={feature.id}
                title={feature.feature_name}
                description={feature.description || "No description available"}
                extractedData="Error loading data. Please try again."
                sources={[]}
                isCustom={isCustom}
                isError={true}
                onDelete={deleteCustomFeature}
                isDeleting={isDeletingFeature}
                refreshTriggered={refreshTriggered}
                onEdit={handleEditFeature}
            />
        );
    }

    const subFeatures = (data?.extraction_results ?? []).map((result) => ({
        title: result.name,
        content: (
            <div className="markdown-wrapper">
                <MarkdownPreview source={result.extracted_text} />
            </div>
        ),
        dataAvailable: result.extracted_text && !result.extracted_text.toLowerCase().startsWith("not explicitly mentioned"),
    }));

    const summary = data.summary || "No summary available";

    return (
        <ContractTransparency
            key={feature.id}
            id={feature.id}
            feature={feature}
            title={feature.feature_name}
            description={feature.description || "No description available"}
            summary={summary}
            sources={[]}
            isCustom={isCustom}
            onDelete={deleteCustomFeature}
            isDeleting={isDeletingFeature}
            onEdit={handleEditFeature}
            refreshTriggered={refreshTriggered}
            subFeatures={subFeatures}
        />
    );
}
