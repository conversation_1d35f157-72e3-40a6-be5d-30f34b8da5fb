'use client';

import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { DropTarget } from '@/utils/drag-drop-types';

interface DroppableAreaProps {
  id: string;
  data: DropTarget;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

export const DroppableArea: React.FC<DroppableAreaProps> = ({
  id,
  data,
  children,
  className = '',
  disabled = false,
}) => {
  const {
    isOver,
    setNodeRef,
  } = useDroppable({
    id,
    data,
    disabled,
  });

  return (
    <div
      ref={setNodeRef}
      className={`${className} ${isOver ? 'bg-blue-50 border-blue-200' : ''}`}
    >
      {children}
    </div>
  );
};
