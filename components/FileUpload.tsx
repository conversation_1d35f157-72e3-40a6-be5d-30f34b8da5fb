"use client";

import React, { ChangeE<PERSON>, DragEvent, RefObject } from "react";
import { motion } from "framer-motion";
import { Upload } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import FolderView from "@/components/FolderView";
import { ALLOWED_FILE_EXTENSIONS } from "@/src/config/documentExtensions";
import {organizeFilesByFolder} from "@/utils/file-upload";

export interface FileUploadAreaProps {
  /** Ref for file input (files only) */
  fileInputRef: RefObject<HTMLInputElement>;
  /** Ref for folder input (directory upload) */
  folderInputRef: RefObject<HTMLInputElement>;
  /** Whether user is dragging over the dropzone */
  isDragging: boolean;
  /** Whether upload/processing is in progress */
  isProcessing: boolean;
  /** Drag events */
  onDragEnter: (e: DragEvent<HTMLDivElement>) => void;
  onDragLeave: (e: DragEvent<HTMLDivElement>) => void;
  onDragOver: (e: DragEvent<HTMLDivElement>) => void;
  onDrop: (e: DragEvent<HTMLDivElement>) => void;
  /** File selection */
  onFileInputChange: (e: ChangeEvent<HTMLInputElement>) => void;
  /** Files and their progress/status */
  files: File[];
  uploadProgress: Record<string, number>;
  uploadStatus: Record<string, "pending" | "success" | "error">;
  /** Remove a file by its name or relative path */
  removeFile: (identifier: string) => void;
}

export const FileUploadArea: React.FC<FileUploadAreaProps> = ({
  fileInputRef,
  folderInputRef,
  isDragging,
  isProcessing,
  onDragEnter,
  onDragLeave,
  onDragOver,
  onDrop,
  onFileInputChange,
  files,
  uploadProgress,
  uploadStatus,
  removeFile,
}) => (
  <>
    <div
      className={`
        w-full border-2 border-dashed rounded-xl p-8 transition-all duration-300 text-center
        ${isDragging ?
          "border-[#21BF61] bg-gradient-to-br from-[#21BF61]/5 to-[#21BF61]/10 scale-[1.02] shadow-lg shadow-[#21BF61]/10"
        :
          "border-gray-200 hover:border-[#21BF61]/50 bg-gradient-to-br from-white to-gray-50 hover:from-white hover:to-[#21BF61]/5"
        }
        ${isProcessing ? "cursor-not-allowed opacity-50" : ""}
      `}
      onDragEnter={isProcessing ? undefined : onDragEnter}
      onDragOver={isProcessing ? undefined : onDragOver}
      onDragLeave={isProcessing ? undefined : onDragLeave}
      onDrop={isProcessing ? undefined : onDrop}
    >
      {/* Hidden file inputs */}
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        multiple
        accept={ALLOWED_FILE_EXTENSIONS.join(',')}
        onChange={onFileInputChange}
        disabled={isProcessing}
      />
      <input
        type="file"
        ref={folderInputRef}
        className="hidden"
        onChange={onFileInputChange}
        disabled={isProcessing}
        {...{ webkitdirectory: "", directory: "" } as any}
      />

      <div className="flex flex-col items-center justify-center py-8">
        <motion.div
          animate={{ y: isDragging ? -10 : 0, scale: isDragging ? 1.1 : 1 }}
          transition={{ type: "spring", stiffness: 300 }}
          className="relative"
        >
          <motion.div
            animate={{
              opacity: isDragging ? 1 : 0.5,
              scale: isDragging ? 1.2 : 1,
            }}
            transition={{ duration: 1, repeat: isDragging ? Infinity : 0, repeatType: "reverse" }}
            className="absolute inset-0 bg-gradient-to-r from-[#21BF61]/20 to-[#21BF61]/30 rounded-full blur-xl"
          />
          <div className="bg-gradient-to-br from-[#21BF61]/20 to-[#21BF61]/30 rounded-full p-6 mb-6 relative">
            <Upload className="h-10 w-10 text-[#21BF61]" />
          </div>
        </motion.div>

        <motion.h3
          animate={{ scale: isDragging ? 1.05 : 1, color: isDragging ? "#21BF61" : "#374151" }}
          className="text-xl font-medium mb-3"
        >
          {isDragging ? "Drop files to upload" : "Drag & drop contract files or folders"}
        </motion.h3>
        <p className="text-gray-500 mb-5">
          <span
            onClick={() => fileInputRef.current?.click()}
            className="text-[#21BF61] font-medium cursor-pointer hover:underline"
          >Browse files</span>
          <span className="mx-2">or</span>
          <span
            onClick={() => folderInputRef.current?.click()}
            className="text-[#21BF61] font-medium cursor-pointer hover:underline"
          >Browse folder</span>
        </p>

        <div className="flex flex-col items-center gap-2">
          {Array.from({ length: Math.ceil(ALLOWED_FILE_EXTENSIONS.length / 5) }, (_, rowIndex) => (
            <div key={rowIndex} className="flex justify-center gap-2">
              {ALLOWED_FILE_EXTENSIONS.slice(rowIndex * 5, rowIndex * 5 + 5).map(ext => (
                <Badge key={ext} variant="outline" className="bg-white/50 text-gray-500">
                  {ext.replace(/^\./, "").toUpperCase()}
                </Badge>
              ))}
            </div>
          ))}
        </div>
      </div>
    </div>

    {/* File list preview */}
    {files.length > 0 && (
      <div className="mt-4 w-full">
        <h3 className="text-xs font-medium mb-2">Uploaded Files ({files.length})</h3>
        <div className="max-h-[200px] overflow-y-auto overflow-x-auto pr-2">
          <FolderView
            folderStructure={React.useMemo(() => organizeFilesByFolder(files), [files])}
            uploadProgress={uploadProgress}
            uploadStatus={uploadStatus}
            removeFile={removeFile}
            isProcessing={isProcessing}
          />
        </div>
      </div>
    )}
  </>
);
