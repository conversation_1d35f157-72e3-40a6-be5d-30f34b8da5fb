import { Card, CardContent } from "@/components/ui/card"
import { FileText, CheckCircle, InfoIcon } from "lucide-react"

interface TransparencyDashboardProps {
  dimensions: number
  totalSubFeatures: number
  availableSubFeatures: number
}

export function TransparencyDashboard({
  dimensions,
  totalSubFeatures,
  availableSubFeatures,
}: TransparencyDashboardProps) {
  // Calculate percentages

  const availabilityPercentage = Math.round((availableSubFeatures / totalSubFeatures) * 100) || 0
  const unavailableSubFeatures = totalSubFeatures - availableSubFeatures
  const unavailablePercentage = Math.round((unavailableSubFeatures / totalSubFeatures) * 100) || 0

  // SVG donut chart parameters
  const size = 160
  const strokeWidth = 15
  const radius = (size - strokeWidth) / 2
  const circumference = 2 * Math.PI * radius

  // Calculate stroke dasharray values for the donut segments
  const availableStrokeDasharray = `${(availabilityPercentage / 100) * circumference} ${circumference}`
  const unavailableStrokeDasharray = `${(unavailablePercentage / 100) * circumference} ${circumference}`

  // Calculate stroke dashoffset for positioning the segments correctly
  const availableStrokeDashoffset = 0
  const unavailableStrokeDashoffset = -((availabilityPercentage / 100) * circumference)

  return (
    <Card className="mb-6">
      <CardContent className="p-6">
        <div className="flex items-center mb-4">
          <div className="bg-slate-100 rounded-full p-3 mr-4">
            <FileText className="h-6 w-6 text-slate-700" />
          </div>
          <div>
            <h3 className="text-lg font-semibold">Contract Transparency Status</h3>
            <p className="text-sm text-slate-600">Data extraction summary across {dimensions} contract dimensions</p>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-6 mt-4">
          {/* Donut Chart */}
          <div className="flex justify-center items-center">
            <div className="relative">
              <svg width={size} height={size} viewBox={`0 0 ${size} ${size}`} className="transform -rotate-90">
                {/* Background circle */}
                <circle cx={size / 2} cy={size / 2} r={radius} fill="none" stroke="#e2e8f0" strokeWidth={strokeWidth} />

                {/* Available segment (green) */}
                {availabilityPercentage && (<circle
                  cx={size / 2}
                  cy={size / 2}
                  r={radius}
                  fill="none"
                  stroke="#22c55e"
                  strokeWidth={strokeWidth}
                  strokeDasharray={availableStrokeDasharray}
                  strokeDashoffset={availableStrokeDashoffset}
                  strokeLinecap="round"
                />)}

                {/* Unavailable segment (neutral color) */}
                {unavailablePercentage > 0 && (<circle
                  cx={size / 2}
                  cy={size / 2}
                  r={radius}
                  fill="none"
                  stroke="#94A3B8"
                  strokeWidth={strokeWidth}
                  strokeDasharray={unavailableStrokeDasharray}
                  strokeDashoffset={unavailableStrokeDashoffset}
                  strokeLinecap="round"
                />)}
              </svg>

              {/* Percentage in the middle */}
              <div className="absolute inset-0 flex flex-col items-center justify-center">
                <span className="text-3xl font-bold">{availabilityPercentage}%</span>
                <span className="text-xs text-slate-600">Completion</span>
              </div>
            </div>
          </div>

          {/* Summary Cards */}
          <div className="flex-1 grid grid-cols-1 sm:grid-cols-2 gap-4">
            {/* Available Data Card */}
            <Card className="border-l-4 border-l-green-500 shadow-sm">
              <CardContent className="p-4">
                <div className="flex items-center">
                  <div className="bg-green-100 rounded-full p-2 mr-3">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Available Data</p>
                    <div className="flex items-baseline">
                      <span className="text-2xl font-bold mr-2">{availableSubFeatures}</span>
                      <span className="text-sm text-slate-600">elements</span>
                    </div>
                    <p className="text-xs text-green-600 font-medium">{availabilityPercentage}% of total</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Unavailable Data Card - Updated styling and text */}
            <Card className="border-l-4 border-l-slate-400 shadow-sm">
              <CardContent className="p-4">
                <div className="flex items-center">
                  <div className="bg-slate-100 rounded-full p-2 mr-3">
                    <InfoIcon className="h-5 w-5 text-slate-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Information not present</p>
                    <div className="flex items-baseline">
                      <span className="text-2xl font-bold mr-2">{unavailableSubFeatures}</span>
                      <span className="text-sm text-slate-600">elements</span>
                    </div>
                    <p className="text-xs text-slate-600 font-medium">{unavailablePercentage}% of total</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="mt-4 text-sm text-slate-600">
          <p>
            Analysis based on {totalSubFeatures} key contract elements across {dimensions} dimensions.
            {unavailableSubFeatures > 0 &&
              ` ${unavailableSubFeatures} elements could not be found in the contract documents.`}
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
