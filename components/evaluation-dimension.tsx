import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Icons } from "@/components/icons"

interface EvaluationDimensionProps {
  id: string
  title: string
  description: string
  extractedData: string
  recommendation: string
}

export function EvaluationDimension({
  id,
  title,
  description,
  extractedData,
  recommendation,
}: EvaluationDimensionProps) {
  return (
    <Card className="mb-4 border-l-4 border-l-[#A8F0B8]">
      <CardHeader className="bg-[#F1EEEA] pb-2">
        <CardTitle className="text-lg font-semibold">{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="pt-4 space-y-4">
        <div className="text-sm text-muted-foreground">
          <p className="text-foreground font-medium mb-1">Current State</p>
          {extractedData}
        </div>

        <div className="bg-[#A8F0B8] rounded-md p-4">
          <p className="font-medium mb-1">Recommended Action</p>
          <p className="text-sm">{recommendation}</p>
        </div>

        <Link href={`/analysis/${id}`} passHref>
          <Button variant="outline" size="sm" className="w-full mt-2">
            <Icons.arrowRight className="mr-2 h-4 w-4" />
            Details
          </Button>
        </Link>
      </CardContent>
    </Card>
  )
}
