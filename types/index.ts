// Project type
export interface Project {
  id: string
  name: string
  description: string
  createdAt: string
  updatedAt: string
  clientName: string
  caseCode: string
}

// File type
export interface ProjectFile {
  id: string
  name: string
  fileType: string
  size: number
  uploadedAt: string
  fileCategory: "main" | "exhibit" | "schedule" | "attachment"
  contractType: "AMS" | "ADM" | "Infra Managed Services" | string
  status: "uploaded" | "processed" | "error" | "idle"
  // Additional metadata for main files
  metadata?: {
    industry?: string
    contractPeriod?: string
    effectiveDate?: string
    expirationDate?: string
    totalValue?: string
    currency?: string
    renewalTerms?: string
  }
}
