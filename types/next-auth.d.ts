import NextAuth, { DefaultSession, DefaultAccount } from "next-auth"
import { JWT as DefaultJWT }     from "next-auth/jwt"

declare module "next-auth" {
  /**
   * Returned by `useSession`, `getSession` etc.
   */
  interface Session extends DefaultSession {
    accessToken: string
    idToken:     string
    error?:      string
    role?:       string[]        // or whatever type your `role` is
    uuid:       string
  }

  /**
   * Provided to the `jwt` callback as `account`
   * and available in subsequent callbacks.
   */
  interface Account extends DefaultAccount {
    access_token:  string
    id_token:      string
    refresh_token: string
    expires_at:    number
    token_type?:   string
    scope?:        string
  }
}

declare module "next-auth/jwt" {
  /**
   * JWT payload stored in the token
   */
  interface JWT extends DefaultJWT {
    accessToken:        string
    accessTokenExpires: number
    refreshToken:      string
    idToken:           string
    error:             string
    role:              string[]
    uuid:              string
  }
}


declare module "jwt-decode" {
  export interface JwtPayload {
    role: string[];
    uid: string;
  }
}