import type React from "react";
import "@/styles/globals.css";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { Toaster } from "@/components/ui/toaster";
import { Providers } from "./providers";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "IT Cost Takeout Analysis",
  description:
    "A web application for IT consultants to analyze contract data for cost takeout opportunities.",
  generator: "v0.dev",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" data-color-mode="light" className="min-h-screen">
      <Providers>
        <body className={`${inter.className} min-h-screen`}>
          {children}
          <Toaster />
        </body>
      </Providers>
    </html>
  );
}
