"use client";
import {signIn, signOut} from "next-auth/react";
import {Line<PERSON><PERSON>} from "lucide-react";
import Link from "next/link";
import {<PERSON><PERSON>} from "@/components/ui/button";
import type React from "react";

export default function LoginPage() {
    return (
        <>
            <header className="container mx-auto px-4 py-6 flex justify-between items-center">
                <Link href="/">
                    <div className="flex items-center space-x-2 cursor-pointer">
                        <div className="bg-primary rounded-md p-2">
                            <LineChart className="h-6 w-6 text-primary-foreground"/>
                        </div>
                        <span className="font-bold text-xl">BCG Cost.AI</span>
                    </div>
                </Link>
            </header>
            <div className="min-h-screen flex flex-col items-center justify-center p-6">
                <h1 className="text-2xl font-bold mb-4">You must be logged in to access this page.</h1>
                <button
                    onClick={() => signIn("okta", { callbackUrl: "/dashboard" })}
                    className="px-4 py-2 bg-primary text-white rounded"
                >
                    Log in with Okta
                </button>
            </div>
        </>
)
    ;
}