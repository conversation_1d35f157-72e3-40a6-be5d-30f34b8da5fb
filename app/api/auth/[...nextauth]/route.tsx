import NextAuth from "next-auth";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "next-auth/providers/okta";
import * as jwt_decode from "jwt-decode";
import { v5 as uuidv5 } from "uuid";


const handler = NextAuth({
  providers: [
    OktaProvider({
      clientId: process.env.OKTA_CLIENT_ID!,
      clientSecret: process.env.OKTA_CLIENT_SECRET!,
      issuer: process.env.OKTA_ISSUER,
      wellKnown: `${process.env.OKTA_ISSUER}/.well-known/openid-configuration`,
      authorization: {
        params: { scope: "openid profile email offline_access" },
      },
    }),
  ],
  secret: process.env.NEXTAUTH_SECRET,

  session: {
    strategy: "jwt",
  },
    callbacks: {
    async jwt({ token, account }) {
      // 1) On initial sign-in, stash all three tokens + compute expiry
      if (account) {
        token.accessToken        = account.access_token!;
        token.idToken            = account.id_token;
        token.refreshToken       = account.refresh_token;

        // account.expires_at is in seconds → convert to ms
        token.accessTokenExpires = (account.expires_at ?? 0) * 1000;
        // Decode the ID token to extract user's role and generate UUID
        try {
          const decoded = jwt_decode.jwtDecode(account.access_token!)
          token.role = decoded.role;
          token.uuid = uuidv5(decoded.uid, uuidv5.URL);
        } catch (err) {
          console.error("Error decoding ID token for role:", err);
        }
        return token;
      }

      // 2) If we haven’t expired yet, just return the existing token
      if (Date.now() < (token.accessTokenExpires as number)) {
        return token;
      }

      // 3) Otherwise, refresh it
      return await refreshAccessToken(token);
    },

    async session({ session, token }) {
      // expose what you need on the client
      session.accessToken  = token.accessToken;
      session.idToken      = token.idToken;
      session.error        = (token as any).error;     // optional: surface refresh errors
      // Expose user's role on session
      session.role = (token as any).role;
      session.uuid  = (token as any).uuid;
      return session;
    },
  },
});
export { handler as GET, handler as POST };

async function refreshAccessToken(token: any) {
  try {
    const url = `${process.env.OKTA_ISSUER}/v1/token`;
    const params = new URLSearchParams({
      grant_type:    "refresh_token",
      refresh_token: token.refreshToken,
      client_id:     process.env.OKTA_CLIENT_ID!,
      client_secret: process.env.OKTA_CLIENT_SECRET!,
    });

    const response = await fetch(url, {
      method:  "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body:    params.toString(),
    });

    const refreshed = await response.json();
    if (!response.ok) {
      throw refreshed;
    }

    return {
      ...token,
      accessToken:        refreshed.access_token,
      // expires_in is in seconds → compute future ms timestamp
      accessTokenExpires: Date.now() + refreshed.expires_in * 1000,
      // Okta may rotate the refresh token too
      refreshToken:       refreshed.refresh_token ?? token.refreshToken,
    };
  } catch (err) {
    console.error("Error refreshing access token:", err);
    return {
      ...token,
      error: "RefreshAccessTokenError",
    };
  }
}