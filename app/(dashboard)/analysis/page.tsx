import { EvaluationDimension } from "@/components/evaluation-dimension"

// This would typically come from your data fetching logic
const getAnalysisData = () => {
  return [
    {
      id: "sla-benchmarking",
      title: "SLA Benchmarking",
      description: "Compare SLAs with industry norms or similar contracts",
      extractedData: "Incident Response Time: 4 hours, Resolution Time: 8 hours, Uptime: 99.9%",
      recommendation: "Consider tightening response time to 2 hours for critical incidents.",
    },
    // ... other dimensions
  ]
}

export default function AnalysisPage() {
  const analysisData = getAnalysisData()

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold tracking-tight">Analysis Overview</h1>
      <div className="grid gap-6 md:grid-cols-2">
        {analysisData.map((dimension) => (
          <EvaluationDimension
            key={dimension.id}
            id={dimension.id}
            title={dimension.title}
            description={dimension.description}
            extractedData={dimension.extractedData}
            recommendation={dimension.recommendation}
          />
        ))}
      </div>
    </div>
  )
}
