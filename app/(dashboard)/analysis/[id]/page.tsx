"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON>, useR<PERSON>er, useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Icons } from "@/components/icons"
import { ChatPanel } from "@/components/chat-panel"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

// Update the component to include tabs for the three different views
export default function DimensionDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isChatOpen, setIsChatOpen] = useState(false)

  const id = params.id as string
  const projectId = searchParams.get("projectId")
  const initialTab = searchParams.get("tab") || "transparency"
  const dimensionData = getDimensionData(id)

  const handleBackToProject = () => {
    if (projectId) {
      router.push(`/project/${projectId}?tab=${initialTab === "transparency" ? "transparency" : initialTab}`)
    } else {
      router.back()
    }
  }

  return (
    <div className="flex h-full flex-col">
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight mb-2">{dimensionData.title}</h1>
        <Button variant="outline" size="sm" onClick={handleBackToProject}>
          <Icons.arrowLeft className="mr-2 h-4 w-4" />
          Back to Project
        </Button>
      </div>

      <div className="flex flex-1 overflow-hidden">
        <div className={`flex-1 overflow-auto ${isChatOpen ? "w-2/3" : "w-full"}`}>
          <Tabs defaultValue={initialTab}>
            <TabsList className="neumorphism-inset mb-4">
              <TabsTrigger value="transparency">Contract Transparency</TabsTrigger>
              <TabsTrigger value="benchmarking">Benchmarking</TabsTrigger>
              <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
            </TabsList>

            {/* Contract Transparency Tab */}
            <TabsContent value="transparency">
              <Card>
                <CardHeader className="bg-[#F1EEEA]">
                  <CardTitle>{dimensionData.title}</CardTitle>
                  <CardDescription>{dimensionData.description}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 pt-6">
                  <div>
                    <h3 className="font-semibold mb-2">Extracted Data</h3>
                    <p>{dimensionData.extractedData}</p>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-2">Sources from Original Documents</h3>
                    <ul className="list-disc pl-5 space-y-2">
                      {dimensionData.sources.map((source, index) => (
                        <li key={index}>
                          <p className="italic mb-1">"{source.text}"</p>
                          <p className="text-sm text-gray-600">
                            Source: {source.metadata.fileName}, Page: {source.metadata.page}, Section:{" "}
                            {source.metadata.section}
                          </p>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-2">Cost Breakdown</h3>
                    <div className="bg-[#F1EEEA] rounded-md p-4">
                      <p className="mb-2">The following costs were identified in this dimension:</p>
                      <ul className="list-disc pl-5 space-y-1">
                        {dimensionData.costBreakdown?.map((item, index) => (
                          <li key={index} className="text-sm">
                            <span className="font-medium">{item.category}:</span> {item.value}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Benchmarking Tab */}
            <TabsContent value="benchmarking">
              <Card>
                <CardHeader className="bg-[#F1EEEA]">
                  <CardTitle>{dimensionData.title} - Benchmarking</CardTitle>
                  <CardDescription>Comparative analysis against industry standards</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 pt-6">
                  <div>
                    <h3 className="font-semibold mb-2">Current State</h3>
                    <p>{dimensionData.extractedData}</p>
                  </div>

                  <div className="space-y-4">
                    <h3 className="font-semibold">Industry Comparison</h3>

                    <div className="space-y-1">
                      <div className="flex justify-between">
                        <span>Industry Percentile</span>
                        <span
                          className={`font-medium ${dimensionData.benchmarkData.percentile < 50 ? "text-red-500" : "text-green-600"}`}
                        >
                          {dimensionData.benchmarkData.percentile}%
                        </span>
                      </div>
                      <Progress
                        value={dimensionData.benchmarkData.percentile}
                        className="h-3"
                        indicatorClassName={
                          dimensionData.benchmarkData.percentile >= 75
                            ? "bg-[#21BF61]"
                            : dimensionData.benchmarkData.percentile >= 50
                              ? "bg-[#FFE68F]"
                              : "bg-[#FF9F9F]"
                        }
                      />
                      <p className="text-sm text-right text-muted-foreground">
                        {dimensionData.benchmarkData.percentile >= 75
                          ? "Above Average"
                          : dimensionData.benchmarkData.percentile >= 50
                            ? "Average"
                            : "Below Average"}
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div className="bg-[#F1EEEA] rounded-md p-4">
                        <h4 className="font-medium mb-2">Industry Average</h4>
                        <p className="text-sm">{dimensionData.benchmarkData.industryAverage}</p>
                      </div>
                      <div className="bg-[#F1EEEA] rounded-md p-4">
                        <h4 className="font-medium mb-2">Peer Comparison</h4>
                        <p className="text-sm">{dimensionData.benchmarkData.peerComparison}</p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-2">Benchmark Details</h3>
                    <Accordion type="single" collapsible className="w-full">
                      {dimensionData.benchmarkDetails?.map((detail, index) => (
                        <AccordionItem key={index} value={`detail-${index}`}>
                          <AccordionTrigger>{detail.title}</AccordionTrigger>
                          <AccordionContent>
                            <p className="mb-2">{detail.description}</p>
                            {detail.data && (
                              <ul className="list-disc pl-5 space-y-1">
                                {detail.data.map((item, idx) => (
                                  <li key={idx} className="text-sm">
                                    {item}
                                  </li>
                                ))}
                              </ul>
                            )}
                          </AccordionContent>
                        </AccordionItem>
                      ))}
                    </Accordion>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Recommendations Tab */}
            <TabsContent value="recommendations">
              <Card>
                <CardHeader className="bg-[#F1EEEA]">
                  <CardTitle>{dimensionData.title} - Recommendations</CardTitle>
                  <CardDescription>Suggested actions for cost optimization</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 pt-6">
                  <div>
                    <h3 className="font-semibold mb-2">Current State</h3>
                    <p>{dimensionData.extractedData}</p>
                  </div>

                  <div className="bg-[#F1EEEA] rounded-md p-4">
                    <h3 className="font-semibold mb-2">Recommended Action</h3>
                    <p>{dimensionData.recommendation}</p>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-2">Reasoning</h3>
                    <p>{dimensionData.reasoning}</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="border rounded-md p-4 bg-[#F1EEEA]/30">
                      <h4 className="font-medium mb-2">Potential Savings</h4>
                      <Badge className="bg-[#F1EEEA]/60">{dimensionData.potentialSavings}</Badge>
                    </div>
                    <div className="border rounded-md p-4 bg-[#F1EEEA]/30">
                      <h4 className="font-medium mb-2">Implementation Complexity</h4>
                      <Badge
                        variant="outline"
                        className={
                          dimensionData.implementationComplexity === "Low"
                            ? "bg-[#F1EEEA]/60"
                            : dimensionData.implementationComplexity === "Medium"
                              ? "bg-[#F1EEEA]/60"
                              : "bg-[#F1EEEA]/60"
                        }
                      >
                        {dimensionData.implementationComplexity || "Medium"}
                      </Badge>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-2">Implementation Steps</h3>
                    <ol className="list-decimal pl-5 space-y-2">
                      {dimensionData.implementationSteps?.map((step, index) => <li key={index}>{step}</li>) || (
                        <>
                          <li>Review current contract terms related to this dimension</li>
                          <li>Prepare amendment proposal based on recommendations</li>
                          <li>Schedule negotiation meeting with vendor</li>
                          <li>Present benchmark data to support your position</li>
                          <li>Finalize and execute contract amendment</li>
                        </>
                      )}
                    </ol>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <div className="mt-4">
            <Button onClick={() => setIsChatOpen(!isChatOpen)}>
              <Icons.messageSquare className="mr-2 h-4 w-4" />
              {isChatOpen ? "Hide Chat" : "Chat about this dimension"}
            </Button>
          </div>
        </div>

        {isChatOpen && (
          <div className="w-1/3 border-l glassmorphism">
            <ChatPanel
              projectId={id}
              onClose={() => setIsChatOpen(false)}
              dimensionTitle={dimensionData.title}
              dimensionDescription={dimensionData.description}
              extractedData={dimensionData.extractedData}
              recommendation={dimensionData.recommendation}
            />
          </div>
        )}
      </div>
    </div>
  )
}

// Update the getDimensionData function to include more realistic data
const getDimensionData = (id: string) => {
  // Determine which dimension to return based on ID
  if (id === "sla-benchmarking") {
    return {
      title: "SLA Benchmarking",
      description: "Compare SLAs with industry norms or similar contracts",
      extractedData: "Incident Response Time: 4 hours, Resolution Time: 8 hours, Uptime: 99.9%",
      recommendation: "Consider tightening response time to 2 hours for critical incidents.",
      details: "Detailed analysis of SLA benchmarking...",
      sources: [
        {
          text: "The Service Provider shall respond to Critical Incidents within 4 hours of notification.",
          metadata: {
            fileName: "MSA_2023.pdf",
            page: 12,
            section: "Service Level Agreements",
          },
        },
        {
          text: "Resolution Time for High Priority issues shall not exceed 8 hours.",
          metadata: {
            fileName: "SLA_Appendix.docx",
            page: 3,
            section: "Incident Management",
          },
        },
      ],
      costBreakdown: [
        { category: "Base Service Fee", value: "$120,000 annually" },
        { category: "Incident Response", value: "Included in base fee" },
        { category: "After-hours Support", value: "$250/hour" },
      ],
      benchmarkData: {
        industryAverage: "Standard response time for critical incidents is 1-2 hours across the industry",
        peerComparison: "Top performers in your industry achieve 30-60 minute response times with 99.99% uptime",
        percentile: 35,
      },
      benchmarkDetails: [
        {
          title: "Response Time Comparison",
          description: "How your response times compare to industry standards",
          data: [
            "Your critical incident response: 4 hours",
            "Industry average: 1-2 hours",
            "Top quartile: 30-60 minutes",
            "Bottom quartile: 6+ hours",
          ],
        },
        {
          title: "Resolution Time Comparison",
          description: "How your resolution times compare to industry standards",
          data: [
            "Your critical incident resolution: 8 hours",
            "Industry average: 4-6 hours",
            "Top quartile: 2-4 hours",
            "Bottom quartile: 12+ hours",
          ],
        },
        {
          title: "Uptime Comparison",
          description: "How your uptime guarantees compare to industry standards",
          data: [
            "Your uptime guarantee: 99.9% (8.76 hours downtime per year)",
            "Industry average: 99.95% (4.38 hours downtime per year)",
            "Top quartile: 99.99% (52.6 minutes downtime per year)",
            "Bottom quartile: 99.5% (43.8 hours downtime per year)",
          ],
        },
      ],
      reasoning:
        "Your current SLA of 4 hours for critical incidents is significantly higher than the industry average of 1-2 hours, placing you in the bottom 35% of comparable contracts. Extended downtime during critical incidents directly impacts business operations and revenue. The industry is trending toward faster response times as infrastructure becomes more mission-critical.",
      potentialSavings: "Estimated $120K-$180K annually in avoided downtime costs",
      implementationComplexity: "Low",
      implementationSteps: [
        "Review current SLA terms in the contract",
        "Draft amendment with new 2-hour response time requirement",
        "Present benchmark data to vendor showing industry standards",
        "Negotiate updated SLA terms",
        "Implement monitoring to track vendor compliance with new SLAs",
      ],
      bestPractices: [
        {
          text: "Industry-leading SLAs typically require a response time of 15-30 minutes for critical incidents, with resolution times under 4 hours.",
          source: "IT Service Management Benchmarks 2024",
        },
        {
          text: "Example clause: 'Vendor shall respond to Critical Incidents within 30 minutes and resolve within 2 hours, or face penalties of 5% of the monthly fee per hour of delay.'",
          source: "Best-in-class MSA Template",
        },
        {
          rule: "SLAs should be tied to business impact, with clear definitions of incident severity levels and corresponding response and resolution times.",
        },
      ],
    }
  } else if (id === "unit-cost-shoring") {
    return {
      title: "Unit Cost/Shoring",
      description: "Check pricing model, shoring model & unit cost for onshore/offshore resources",
      extractedData:
        "Pricing Model: Time & Materials, Shoring: 70% Offshore, 30% Onshore, Unit Cost: $50/hour offshore, $150/hour onshore",
      recommendation: "Consider increasing offshore ratio to 80% for non-critical tasks to reduce overall costs.",
      details: "Analysis of resource distribution and unit costs...",
      sources: [
        {
          text: "Services shall be delivered using a blended delivery model with 70% offshore and 30% onshore resources.",
          metadata: {
            fileName: "SOW_2023.pdf",
            page: 8,
            section: "Delivery Model",
          },
        },
        {
          text: "Offshore rate: $50 per hour. Onshore rate: $150 per hour.",
          metadata: {
            fileName: "Pricing_Schedule.xlsx",
            page: 2,
            section: "Rate Card",
          },
        },
      ],
      costBreakdown: [
        { category: "Annual Onshore Costs", value: "$1.4M (30% @ $150/hr)" },
        { category: "Annual Offshore Costs", value: "$2.2M (70% @ $50/hr)" },
        { category: "Blended Hourly Rate", value: "$80/hour" },
      ],
      benchmarkData: {
        industryAverage: "Typical offshore ratios for IT services range from 75-85% with blended rates of $65-75/hour",
        peerComparison: "Leading organizations achieve 85-90% offshore leverage with selective nearshore elements",
        percentile: 30,
      },
      benchmarkDetails: [
        {
          title: "Offshore Ratio Comparison",
          description: "How your offshore/onshore mix compares to industry benchmarks",
          data: [
            "Your offshore ratio: 70%",
            "Industry average: 75-85%",
            "Top performers: 85-90%",
            "Bottom quartile: below 70%",
          ],
        },
        {
          title: "Rate Comparison",
          description: "How your rates compare to market standards",
          data: [
            "Your offshore rate: $50/hour (on par with market)",
            "Your onshore rate: $150/hour (slightly above market avg of $135-145/hour)",
            "Your blended rate: $80/hour (above market avg of $65-75/hour)",
          ],
        },
        {
          title: "Function Distribution Analysis",
          description: "Distribution of functions between offshore and onshore teams",
          data: [
            "Project management: 80% onshore (industry avg: 60% onshore)",
            "Development: 75% offshore (industry avg: 85% offshore)",
            "Testing: 85% offshore (industry avg: 90% offshore)",
            "Support: 75% offshore (industry avg: 85% offshore)",
          ],
        },
      ],
      reasoning:
        "Your current offshore ratio (70%) is below industry benchmarks, resulting in higher overall costs. Detailed workload analysis indicates that at least 10-15% of current onshore work could be performed offshore with appropriate knowledge transfer and governance. Your offshore rates ($50/hr) are competitive, but the overall mix is suboptimal.",
      potentialSavings: "Estimated $280K-$420K annual savings by shifting 10% of work from onshore to offshore",
      implementationComplexity: "Medium",
      implementationSteps: [
        "Perform detailed function analysis to identify tasks suitable for offshore delivery",
        "Develop transition plan for shifting identified functions",
        "Negotiate contract amendment for revised offshore percentages",
        "Implement knowledge transfer plan with clearly defined milestones",
        "Update governance model to support increased offshore delivery",
        "Establish metrics to track performance and quality of transitioned functions",
      ],
    }
  } else {
    // Default dimension data for any other ID
    return {
      title: "SLA Benchmarking",
      description: "Compare SLAs with industry norms or similar contracts",
      extractedData: "Incident Response Time: 4 hours, Resolution Time: 8 hours, Uptime: 99.9%",
      recommendation: "Consider tightening response time to 2 hours for critical incidents.",
      details: "Detailed analysis of SLA benchmarking...",
      sources: [
        {
          text: "The Service Provider shall respond to Critical Incidents within 4 hours of notification.",
          metadata: {
            fileName: "MSA_2023.pdf",
            page: 12,
            section: "Service Level Agreements",
          },
        },
        {
          text: "Resolution Time for High Priority issues shall not exceed 8 hours.",
          metadata: {
            fileName: "SLA_Appendix.docx",
            page: 3,
            section: "Incident Management",
          },
        },
      ],
      costBreakdown: [
        { category: "Base Service Fee", value: "$120,000 annually" },
        { category: "Incident Response", value: "Included in base fee" },
        { category: "After-hours Support", value: "$250/hour" },
      ],
      benchmarkData: {
        industryAverage: "Standard response time for critical incidents is 1-2 hours across the industry",
        peerComparison: "Top performers in your industry achieve 30-60 minute response times with 99.99% uptime",
        percentile: 35,
      },
      benchmarkDetails: [
        {
          title: "Response Time Comparison",
          description: "How your response times compare to industry standards",
          data: [
            "Your critical incident response: 4 hours",
            "Industry average: 1-2 hours",
            "Top quartile: 30-60 minutes",
            "Bottom quartile: 6+ hours",
          ],
        },
        {
          title: "Resolution Time Comparison",
          description: "How your resolution times compare to industry standards",
          data: [
            "Your critical incident resolution: 8 hours",
            "Industry average: 4-6 hours",
            "Top quartile: 2-4 hours",
            "Bottom quartile: 12+ hours",
          ],
        },
      ],
      reasoning:
        "Your current SLA of 4 hours for critical incidents is significantly higher than the industry average of 1-2 hours, placing you in the bottom 35% of comparable contracts. Extended downtime during critical incidents directly impacts business operations and revenue. The industry is trending toward faster response times as infrastructure becomes more mission-critical.",
      potentialSavings: "Estimated $120K-$180K annually in avoided downtime costs",
      implementationComplexity: "Low",
      implementationSteps: [
        "Review current SLA terms in the contract",
        "Draft amendment with new 2-hour response time requirement",
        "Present benchmark data to vendor showing industry standards",
        "Negotiate updated SLA terms",
        "Implement monitoring to track vendor compliance with new SLAs",
      ],
    }
  }
}
