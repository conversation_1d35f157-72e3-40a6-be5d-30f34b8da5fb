"use client"

import { useSession, signOut } from "next-auth/react";
import { But<PERSON> } from "@/components/ui/button";
import type React from "react"
import Link from "next/link"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { data: session } = useSession();
  return (
    <div className="flex flex-col min-h-screen bg-background">
      <div className="flex flex-1 flex-col">
        <header className="border-b border-border bg-white py-4 px-6 flex justify-between items-center">
          <div className="flex items-center">
            <div className="bg-primary rounded-md p-2 mr-3">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M12 2L2 7L12 12L22 7L12 2Z"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M2 17L12 22L22 17"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M2 12L12 17L22 12"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <Link href="/dashboard" className="text-xl font-bold text-foreground hover:underline">
              BCG Cost.AI
            </Link>
          </div>
          <div className="flex items-center space-x-4">
            {session?.user && (
              <>
                <span className="text-sm text-foreground">{session.user.name}</span>
                <Button variant="outline" onClick={() => signOut({ callbackUrl: "/" })}>
                  Log Out
                </Button>
              </>
            )}
          </div>
        </header>
        <main className="flex-1 p-6 md:p-8 bg-gradient-to-br from-white to-muted/30">{children}</main>
        <footer className="border-t border-border py-4 px-6 text-center text-sm text-muted-foreground">
          © {new Date().getFullYear()} BCG. All rights reserved.
        </footer>
      </div>
    </div>
  )
}
