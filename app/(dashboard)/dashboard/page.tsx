"use client";

import { formatDate } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import Link from "next/link";
import { Icons } from "@/components/icons";
import { useProjects } from "@/api/projects";
import { format, parseISO } from 'date-fns';


export default function DashboardPage() {
  const { data: fetchedProjects } = useProjects();
  const userProjects = fetchedProjects?.map((project) => {
    return {
      id: project.id,
      name: project.name,
      clientName: project.client,
      caseCode: project.case_code,
      description: project.description,
      createdAt: format(parseISO(project.created_at), 'dd.MM.yyyy'),
    };
  });

  const projects = [...(userProjects ?? [])];

  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-foreground mb-2">
            Projects Dashboard
          </h1>
          <p className="text-muted-foreground">
            Manage and analyze your contract optimization projects
          </p>
        </div>
        <Link href="/create-project" passHref>
          <Button className="bg-primary hover:bg-primary-shade text-white">
            <Icons.add className="mr-2 h-4 w-4" />
            Create Project
          </Button>
        </Link>
      </div>

      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {projects.map((project) => (
          <Card
            key={project.id}
            className="overflow-hidden hover:shadow-md transition-shadow border border-border"
          >
            <CardHeader className="p-6 bg-gradient-to-r from-primary-light/10 to-primary/10">
              <CardTitle className="line-clamp-1 text-foreground">
                {project.name}
              </CardTitle>
              <CardDescription className="text-muted-foreground font-medium">
                {project.clientName}
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6 pt-4">
              <div className="flex items-center text-sm text-muted-foreground mb-3">
                <Icons.fileText className="mr-2 h-4 w-4 text-primary" />
                <span>Case Code: {project.caseCode}</span>
              </div>
              <p className="line-clamp-2 text-sm text-foreground/80">
                {project.description}
              </p>
            </CardContent>
            <CardFooter className="flex items-center justify-between border-t border-border p-6 bg-muted/30">
              <p className="text-sm text-muted-foreground">
                Created: {project.createdAt}
              </p>
              <Button
                asChild
                size="sm"
                className="bg-primary hover:bg-primary-shade text-white"
              >
                <Link href={`/project/${project.id}/file-management`}>View Project</Link>
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>

      {projects.length === 0 && (
        <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-12 text-center">
          <div className="rounded-full bg-primary/10 p-4 mb-4">
            <Icons.fileText className="h-10 w-10 text-primary" />
          </div>
          <h3 className="text-lg font-semibold text-foreground mb-2">
            No projects yet
          </h3>
          <p className="text-muted-foreground mb-6 max-w-md">
            Create your first project to start analyzing contracts and finding
            optimization opportunities
          </p>
          <Link href="/create-project" passHref>
            <Button className="bg-primary hover:bg-primary-shade text-white">
              <Icons.add className="mr-2 h-4 w-4" />
              Create Project
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
}
