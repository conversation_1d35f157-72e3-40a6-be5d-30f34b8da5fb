"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  Database,
} from "lucide-react";
import { formatBytes } from "@/lib/utils";
import { useProjectFileStore } from "@/store/project-file-store";
import { toast } from "react-toastify";
import { useTriggerIngestion, useUploadListing } from "@/api/document";
import type { components } from "@/api/api-types";
import {getFileIcon} from "@/components/icons";

// Type aliases from OpenAPI
type FileDTO = components["schemas"]["FileDTO"];
type ClusterDTO = components["schemas"]["ClusterDTO"];

// Extended Cluster/File with frontend-only normalizedScore
type ExtendedCluster = ClusterDTO & {
  files: (FileDTO & { normalizedScore?: number })[];
};

export default function DeduplicationReviewPage() {
  const router = useRouter();
  const files = useProjectFileStore((state) => state.files);
  const setFiles = useProjectFileStore((state) => state.setFiles);
  const projectId = useProjectFileStore((state) => state.projectId);
  const deduplicationData = useProjectFileStore((state) => state.deduplicationData);
  const setDeduplicatedFileNames = useProjectFileStore((state) => state.setDeduplicatedFileNames);
  const { mutate: uploadListing } = useUploadListing();

  const [modifiedClusters, setModifiedClusters] = useState<ExtendedCluster[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  const normalizeScores = (clusters: ClusterDTO[]): ExtendedCluster[] => {
    return clusters.map((cluster) => {
      const maxScore = Math.max(...cluster.files.map((file) => file.score));
      return {
        ...cluster,
        files: cluster.files.map((file) => ({
          ...file,
          normalizedScore: maxScore > 0 ? Math.round((file.score / maxScore) * 100) : 0,
        })),
      };
    });
  };

  useEffect(() => {
    if (!deduplicationData) {
      router.push("/create-project");
      return;
    }
    const filtered = deduplicationData.clusters.filter((c) => c.files.length > 1);
    if (filtered.length === 0) {
      console.log("No clusters with duplicates found, redirecting to create project");
      handleUploadFilesAndTriggerIngestion();
      return;
    }
    setModifiedClusters(normalizeScores(filtered));
  }, [deduplicationData, router]);

  const selectFile = (clusterId: number, fileName: string, is_existing: boolean) => {
    setModifiedClusters((prev) =>
      prev.map((cluster) =>
        cluster.cluster_id === clusterId
          ? {
            ...cluster,
            files: cluster.files.map((file) => ({
              ...file,
              selected: file.file_name === fileName && file.is_existing === is_existing,
            })),
          }
          : cluster
      )
    );
  };

  const getUnselectedFiles = (): string[] => {
    return modifiedClusters.flatMap((cluster) =>
      cluster.files.filter((file) => !file.selected).map((file) => file.file_name)
    );
  };

  const handleUploadFilesAndTriggerIngestion = () => {
    setIsSubmitting(true);
    const unselectedFiles = getUnselectedFiles();
    setDeduplicatedFileNames(unselectedFiles);
    const filteredFormData = new FormData();

    const selectedFilesMap = new Map<string, {name: string, isExisting: boolean}[]>();

    modifiedClusters.forEach(cluster => {
      const selectedFile = cluster.files.find(f => f.selected);
      if (selectedFile) {
        selectedFilesMap.set(selectedFile.file_name, cluster.files.filter(f => !f.selected).map(f => ({name: f.file_name, isExisting: f.is_existing})));
      }
    });

    const selectedFiles = modifiedClusters.flatMap(cluster =>
      cluster.files.filter(file => file.selected || cluster.files.length === 1).map(file => ({name: file.file_name, isExisting: file.is_existing}))
    );

    const filteredFiles = files.filter(
      file => selectedFiles.find(({name, isExisting}) => name === file.file.name && isExisting === false) || !unselectedFiles.includes(file.file.webkitRelativePath)
    ).map(file => {
        const replacedFile = selectedFilesMap.get(file.file.name)?.find(({isExisting}) => isExisting === true);

        return {
          ...file,
          replacedFile: replacedFile && file ? replacedFile.name : undefined
        };
      });

    setFiles(filteredFiles);

    filteredFiles.forEach((file) => {
      filteredFormData.append("files", file.file, file.relativePath);
    });


    uploadListing({
      files: filteredFormData,
      projectId
    }, {
      onSuccess: (data) => {
        if (data.failed_files && data.failed_files.length > 0) {
          data.failed_files.forEach(failedFile => {
            toast.error(`File "${failedFile.name}" couldn't be uploaded because: ${failedFile.reason}`);
          });
        }

        if (projectId) {
          router.push(`/project/${projectId}/analysis-results`);
        } else {
          toast.error("Project ID is missing");
        }
      },
      onError: (error) => toast.error(`Error in Uploading Files: ${error.message}`),
    });
  };

  const getFileNameWithoutExtension = (name: string) => name.split(".").slice(0, -1).join(".") || name;

  const getDocumentGroupName = (cluster: ExtendedCluster) => {
    const selected = cluster.files.find((f) => f.selected);
    return getFileNameWithoutExtension(selected?.file_name || cluster.files[0]?.file_name || "Unknown");
  };

  const totalPages = Math.ceil(modifiedClusters.length / itemsPerPage);
  const paginatedClusters = modifiedClusters.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);
  const totalDuplicateFiles = modifiedClusters.reduce((count, cluster) => count + cluster.files.length - 1, 0);

  if (!deduplicationData) {
    return <div className="min-h-screen flex items-center justify-center">Redirecting...</div>;
  }

  if (!modifiedClusters.length) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 py-12">
      <div className="container mx-auto px-4">
        <Button variant="ghost" className="mb-8" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Back
        </Button>

        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
          <h1 className="text-3xl font-bold mb-3">Remove duplicates</h1>
          <p className="text-gray-600 text-lg mb-6">
            We identified similar documents. Select which version of each document to keep.
          </p>

          <Card className="border-[#21BF61]/20 shadow-sm mb-8">
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold mb-2">Deduplication Summary</h2>
              <p className="text-gray-600">
                {deduplicationData.deduplication_summary.total_files_processed} files processed, {totalDuplicateFiles} duplicates found.
              </p>
            </CardContent>
          </Card>

          {paginatedClusters.map((cluster) => (
            <Card key={cluster.cluster_id} className="mb-6">
              <CardHeader className="bg-gray-50 border-b">
                <CardTitle>{getDocumentGroupName(cluster)}</CardTitle>
                <CardDescription>{cluster.files.length} versions</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <RadioGroup
                  defaultValue={
                    `${cluster.files.find((f) => f.selected)?.file_name}-${cluster.files.find((f) => f.selected)?.is_existing}`
                    || `${cluster.files[0]?.file_name}-${cluster.files[0]?.is_existing}`
                  }
                  className="divide-y"
                >
                  {cluster.files.map((file) => (
                    <div
                      key={`${file.file_name}-${file.is_existing}`}
                      className={`p-4 flex items-center justify-between ${
                        file.selected
                          ? file.is_existing
                            ? "bg-orange-50 border-l-4 border-orange-200 ring-2 ring-[#21BF61]/30 ring-inset"
                            : "bg-[#21BF61]/5"
                          : file.is_existing
                            ? "bg-orange-50 border-l-4 border-orange-200"
                            : ""
                      }`}
                    >
                      <div className="flex items-center space-x-4 w-full">
                        <RadioGroupItem
                          value={`${file.file_name}-${file.is_existing}`}
                          id={`file-${cluster.cluster_id}-${file.file_name}-${file.is_existing}`}
                          className="data-[state=checked]:border-[#21BF61]"
                          onClick={(e) => {
                            e.stopPropagation();
                            console.log("File selected:", file.file_name, file.is_existing);
                            selectFile(cluster.cluster_id, file.file_name, file.is_existing);
                          }}
                        />
                        {getFileIcon(file.file_name)}
                        <label htmlFor={`file-${cluster.cluster_id}-${file.file_name}-${file.is_existing}`} className="w-full">
                          <p className={`font-medium ${file.is_existing ? "text-orange-700" : ""}`}>
                            {file.file_name}
                            {file.is_existing && <span className="ml-2 text-xs text-orange-500">(Existing)</span>}
                          </p>
                          <p className="text-sm text-gray-500">
                            {formatBytes(file.file_size_kb * 1024)} • Quality score: {(file as any).normalizedScore ?? Math.round(file.score)}%
                          </p>
                        </label>
                      </div>
                      <div className="flex gap-2">
                        {file.is_existing &&
                          <Badge className="bg-orange-100 text-orange-700 border-orange-200 flex items-center gap-1">
                            <Database className="h-3 w-3" />
                            Existing
                          </Badge>
                        }
                        {file.selected && <Badge className="bg-[#21BF61]/20 text-[#21BF61]">Selected</Badge>}
                      </div>
                    </div>
                  ))}
                </RadioGroup>
              </CardContent>
            </Card>
          ))}

          {totalPages > 1 && (
            <div className="flex justify-center gap-2 mt-6">
              <Button onClick={() => setCurrentPage((p) => Math.max(p - 1, 1))} disabled={currentPage === 1}>
                Previous
              </Button>
              {Array.from({ length: totalPages }, (_, i) => (
                <Button
                  key={i + 1}
                  variant={currentPage === i + 1 ? "default" : "outline"}
                  onClick={() => setCurrentPage(i + 1)}
                >
                  {i + 1}
                </Button>
              ))}
              <Button onClick={() => setCurrentPage((p) => Math.min(p + 1, totalPages))} disabled={currentPage === totalPages}>
                Next
              </Button>
            </div>
          )}

          <div className="flex justify-end mt-10">
            <Button onClick={handleUploadFilesAndTriggerIngestion} disabled={isSubmitting} size="lg" className="px-8">
              {isSubmitting ? "Processing..." : <>Continue <ArrowRight className="ml-2 h-4 w-4" /></>}
            </Button>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
