"use client";

import {useSession} from "next-auth/react";
import React, {useEffect, useMemo, useRef, useState} from "react";
import {Transition} from "@headlessui/react";
import {useParams, usePathname, useRouter, useSearchParams} from "next/navigation";
import {Button} from "@/components/ui/button";
import {Icons} from "@/components/icons";
import {cn, formatDate} from "@/lib/utils";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger,} from "@/components/ui/dropdown-menu";
import {ChatPanel} from "@/components/chat-panel"
import {MinimizedChatBar} from "@/components/minimized-chat-bar"
import {Tabs, TabsContent} from "@/components/ui/tabs"
import {BenchmarkingDashboard} from "@/components/benchmarking-dashboard"
import {TransparencyDashboard} from "@/components/transparency-dashboard"
import {BarChart, FileSpreadsheet, FileText, FileUp, Lightbulb, Lock, PlusCircle, RefreshCw} from "lucide-react"
import {Badge} from "@/components/ui/badge"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {Textarea} from "@/components/ui/textarea"
import Link from "next/link"
import {useProject} from "@/api/projects"
import {
  useCreateCustomTransparencyFeature,
  useGetTransparencyFeatures,
  useUpdateCustomTransparencyFeature
} from "@/api/transparency-features"
import {toast, ToastContainer} from "react-toastify"
import {TransparencyFeatureCard} from "@/components/TransparencyFeatureCard";
import {BenchmarkFeatureCard} from "@/components/BenchmarkFeatureCard";
import {RecommendationFeatureCard} from "@/components/RecommendationFeatureCard";
import {canViewAdmin, canViewBenchmarking, canViewRecommendations, canViewTransparency} from "@/src/config/roles";
import {
  downloadExcel,
  downloadPresentation,
  useExcelStatus,
  usePresentationStatus,
  useRequestContractExcel,
  useRequestPresentation,
} from "@/api/presentations";
import {useGetBenchmarkStatistics, useGetExtractionStatistics} from "@/api/feature_statistics";
import {useContract} from "@/api/contracts";
import {differenceInMonths, parseISO} from "date-fns";

export default function ProjectPage() {
  // Fetch session and use role claim from session
  const { data: session, status } = useSession();
  // Use role claim from session
  const roles: string[] = Array.isArray((session as any).role)
    ? (session as any).role
    : (session as any).role
      ? [(session as any).role]
      : [];
  // Insert session loading check after all hooks
  if (status === "loading") {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-gray-500">Loading session...</p>
      </div>
    );
  }

  const params = useParams();
  const searchParams = useSearchParams();
  const projectId = params.project_id as string;
  const contractId = params.contract_id as string;
  const [chatOpen, setChatOpen] = useState(false);
  const [expandedChat, setExpandedChat] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isExcelLoading, setIsExcelLoading] = useState(false);

  // Animation states
  const [chatPanelAnimation, setChatPanelAnimation] = useState<'slide-up' | 'slide-down' | 'zoom-out' | 'collapse-height' | null>(null);
  const [minimizedChatAnimation, setMinimizedChatAnimation] = useState<'slide-up' | 'slide-down' | 'expand-height' | null>(null);
  const [dialogAnimation, setDialogAnimation] = useState<'zoom-in' | 'zoom-out' | null>(null);

  // Refs for DOM elements
  const chatPanelRef = useRef<HTMLDivElement>(null);
  const minimizedChatRef = useRef<HTMLDivElement>(null);
  const dialogContentRef = useRef<HTMLDivElement>(null);
  const [analysisTab, setAnalysisTab] = useState("transparency");
  const [customFeatures, setCustomFeatures] = useState<
    Array<{
      id: string;
      title: string;
      prompt: string;
      extractedData: string;
      isCustom: boolean;
    }>
  >([]);
  const { data: extractionStatistics, isLoading: isLoadingExtractionStatistics } = useGetExtractionStatistics(
    projectId,
    contractId
  );
  const { data: benchmarkingCounts, isLoading: isLoadingBenchmarkStatistics } = useGetBenchmarkStatistics(projectId, contractId);

  // Router and tab utilities
  const router = useRouter();
  const pathname = usePathname();
  const searchParamsData = useSearchParams();

  const handleTabChange = (value: string) => {
    setAnalysisTab(value);
    const params = new URLSearchParams(searchParamsData.toString());
    params.set('tab', value);
    router.push(`${pathname}?${params.toString()}`);
  };
  const [featureModalOpen, setFeatureModalOpen] = useState(false);
  const [editingFeature, setEditingFeature] = useState<any>(null);
  const { data: userProject } = useProject(projectId);
  const { data: contractData } = useContract(contractId)
  const {
    data: transparencyFeaturesResponse,
    isLoading,
    isError,
  } = useGetTransparencyFeatures(projectId);

  const evaluationDimensions = useMemo(() => {
    if (!transparencyFeaturesResponse) return [];

    return [
      ...transparencyFeaturesResponse.custom_features,
      ...transparencyFeaturesResponse.standard_features,
    ];
  }, [transparencyFeaturesResponse]);

  const { mutate: createCustomFeature, isPending: isCreatingFeature } =
    useCreateCustomTransparencyFeature(projectId);
  const { mutate: updateCustomFeature, isPending: isUpdatingFeature } =
    useUpdateCustomTransparencyFeature(projectId, editingFeature?.id);

  useEffect(() => {
    const tab = searchParams.get("tab");
    if (tab === "recommendations") {
      setAnalysisTab("recommendations");
    }
  }, [searchParams]);

  const [hideHeader, setHideHeader] = useState(false);

  const contentRef = useRef<HTMLDivElement>(null);

  const handleScroll = () => {
    if (contentRef.current && contentRef.current.scrollTop > 10) {
      setHideHeader(true);
    } else {
      setHideHeader(false);
    }
  };

  useEffect(() => {
    const el = contentRef.current;
    if (el) {
      el.addEventListener("scroll", handleScroll);
      return () => el.removeEventListener("scroll", handleScroll);
    }
  }, [handleScroll]);

  const [headerHeight, setHeaderHeight] = useState(0);

  const headerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (headerRef.current) {
      setHeaderHeight(headerRef.current.scrollHeight);
    }
  }, []);

  const [refreshCount, setRefreshCount] = useState(0);
  const [taskId, setTaskId] = useState<string | null>(null);
  const [excelTaskId, setExcelTaskId] = useState<string | null>(null);
  const requestMutation = useRequestPresentation();
  const excelMutation = useRequestContractExcel();
  const { data: presentationStatus, isLoading: isStatusLoading } =
    usePresentationStatus(taskId, { refetchInterval: 2000 });
  const { data: excelStatus, isLoading: isExcelStatusLoading } =
    useExcelStatus(excelTaskId, { refetchInterval: 2000 });
  const isPresentationLoading =
    requestMutation.isPending ||
    isStatusLoading ||
    (taskId !== null && presentationStatus?.status !== "failed");
  useEffect(() => {
    const downloadWhenReady = async () => {
      if (presentationStatus?.status === "completed" && taskId) {
        try {
          await downloadPresentation(taskId, presentationStatus?.file_path);
          toast.success("Presentation downloaded successfully!");
          setTaskId(null); // Reset taskId after successful download
        } catch (error) {
          toast.error(`Download failed: ${error instanceof Error ? error.message : "Unknown error"}`);
        }
      } else if (presentationStatus?.status === "failed") {
        toast.error(
          `Generation failed: ${presentationStatus.error || "Unknown error"}`
        );
        setTaskId(null);
      }
    };

    downloadWhenReady();
  }, [presentationStatus?.status, taskId]);

  useEffect(() => {
    const downloadExcelWhenReady = async () => {
      if (excelStatus?.status === "completed" && excelTaskId) {
        try {
          await downloadExcel(excelTaskId, excelStatus?.file_path);
          toast.success("Excel downloaded successfully!");
        } catch (error) {
          toast.error(`Excel download failed: ${error instanceof Error ? error.message : "Unknown error"}`);
        } finally {
          setExcelTaskId(null);
          setIsExcelLoading(false);
        }
      } else if (excelStatus?.status === "failed") {
        toast.error(`Excel generation failed: ${excelStatus.error || "Unknown error"}`);
        setExcelTaskId(null);
        setIsExcelLoading(false);
      }
    };

    downloadExcelWhenReady();
  }, [excelStatus?.status, excelTaskId]);
  const handleExportData = async () => {
    setIsExcelLoading(false);
    toast.info("Generating presentation...");
    try {
      // Reset any previous task
      setTaskId(null);

      // Start generation and get taskId
      const response = await requestMutation.mutateAsync({ projectId, contractId });

      // Save taskId for status polling
      setTaskId(response.task_id);

      // Polling happens automatically via the hook
      toast.info("Generating presentation...");
    } catch (error) {
      toast.error(`Failed to start presentation generation: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  };

  const handleExportExcel = async () => {
    setIsExcelLoading(true);
    toast.info("Generating Excel file...");
    try {
      const response = await excelMutation.mutateAsync({ projectId, contractId });
      setExcelTaskId(response.task_id);
    } catch (error) {
      toast.error(`Failed to start Excel generation: ${error instanceof Error ? error.message : "Unknown error"}`);
      setIsExcelLoading(false);
    }
  };


  const handleGlobalRefresh = () => {
    setRefreshCount((prev) => prev + 1);

    toast.info("Refreshing data generation for all features...");
  };


  const currentProject = {
    id: userProject?.id,
    name: userProject?.name,
    description: userProject?.description,
    createdAt: userProject?.created_at,
    clientName: userProject?.client,
    caseCode: userProject?.case_code,
  };

  const currentContract = {
    id: contractData?.id,
    fileName: contractData?.name,
    contractType: contractData?.type,
    startDate: contractData?.start_date,
    endDate: contractData?.end_date,
    providerName: contractData?.provider?.name,
    duration: calculateDuration(contractData?.start_date, contractData?.end_date)
  };

  const runAnalysis = () => {
    setAnalysisTab("benchmarking");
  };

  const handleSaveFeature = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const name = formData.get("name") as string;
    const question = formData.get("question") as string;
    const generationInstruction =
      (formData.get("generationInstruction") as string) || null;

    if (editingFeature) {
      updateCustomFeature(
        {
          id: editingFeature.id,
          name,
          question,
          generation_instruction: generationInstruction,
        },
        {
          onSuccess: (data) => {
            toast.success("Custom feature updated successfully");
            setFeatureModalOpen(false);
            setEditingFeature(null);
          },
          onError: (error: any) => {
            toast.error(`Error updating custom feature: ${error.message}`);
          },
        }
      );
    } else {
      createCustomFeature(
        {
          name,
          question,
          generation_instruction: generationInstruction,
        },
        {
          onSuccess: (data) => {
            toast.success(`Custom feature created successfully`);
            setFeatureModalOpen(false);
          },
          onError: (error: any) => {
            toast.error(`Error creating custom feature: ${error.message}`);
          },
        }
      );
    }
  };

  // Function to open the edit modal for a feature
  const handleEditFeature = (feature: any) => {
    setEditingFeature(feature);
    setFeatureModalOpen(true);
  };

  // Function to open the modal for creating a new feature
  const handleAddFeature = () => {
    setEditingFeature(null);
    setFeatureModalOpen(true);
  };

  if (!userProject) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-gray-500">
          Unable to load project data. Please try again later.
        </p>
      </div>
    );
  }

  const handleRemoveFile = (identifier: string) => {
    setSelectedFiles(prev => prev.filter(file => file.name !== identifier));
  };


  // Calculate transparency dashboard metrics
  const totalDimensions = evaluationDimensions?.length || 0;
  const totalSubFeatures = 25;
  const availableSubFeatures = 15;

  return (
    <div className="flex flex-col min-h-screen">
      <Transition
        show={!hideHeader}
        enter="transition-all duration-300 ease-out"
        enterFrom="opacity-0 transform -translate-y-full"
        enterTo="opacity-100 transform translate-y-0"
        leave="transition-all duration-300 ease-in"
        leaveFrom="opacity-100 transform translate-y-0"
        leaveTo="opacity-0 transform -translate-y-full"
      >
        <div className="mb-4">
          <div className="bg-gradient-to-r from-white to-[#F8F7F4] rounded-lg shadow-md border border-[#E8E6E1]">
            <div className="p-4">
              <div className="flex flex-col gap-3">
                <div className="flex justify-between items-start">
                  <div className="flex gap-4">
                    <div className="flex-shrink-0 bg-primary/10 rounded-lg p-3 flex items-center justify-center">
                      <Icons.fileContract className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <h1 className="text-xl font-bold">
                          {currentProject.name}
                        </h1>
                        <span className="px-2 py-0.5 rounded-full text-xs font-medium bg-[#E9F7F2] text-[#096A54]">
                          Active
                        </span>
                      </div>
                      <div className="flex flex-col gap-1.5">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-1.5">
                            <span className="text-sm text-muted-foreground">Client:</span>
                            <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100 px-2 py-0.5">
                              {currentProject.clientName}
                            </Badge>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-1.5">
                            <Icons.tag className="h-3 w-3" />
                            <span className="text-xs text-muted-foreground">Case: {currentProject.caseCode}</span>
                          </div>
                          <div className="flex items-center gap-1.5">
                            <Icons.calendar className="h-3 w-3" />
                            <span className="text-xs text-muted-foreground">Created: {formatDate(currentProject.createdAt)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Link
                      href={`/project/${projectId}/file-management`}
                      passHref
                    >
                      <Button variant="outline" size="sm" className="h-8">
                        <Icons.database className="mr-1 h-3.5 w-3.5" />
                        Manage Data
                      </Button>
                    </Link>
                    <Button variant="outline" size="sm" className="h-8">
                      <Icons.share className="mr-1 h-3.5 w-3.5" />
                      Share
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mt-1">
                  <div className="bg-white rounded-lg p-3 shadow-sm border border-[#E8E6E1]">
                    <h3 className="text-xs font-medium text-muted-foreground">
                      Contract Overview
                    </h3>
                    <div className="text-sm mt-1">
                      <div className="flex items-center gap-1.5">
                        <span className="text-sm text-muted-foreground">Provider:</span>
                        <Badge className="bg-green-100 text-green-800 hover:bg-green-100 px-2 py-0.5">
                          {currentContract.providerName}
                        </Badge>
                      </div>
                      <div className="h-2" /> {/* Adds vertical space */}
                      <div className="flex items-center gap-1.5">
                        <span className="text-sm text-muted-foreground">Contract type:</span>
                        <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100 px-2 py-0.5">
                          {currentContract.contractType}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white rounded-lg p-3 shadow-sm border border-[#E8E6E1]">
                    <h3 className="text-xs font-medium text-muted-foreground">
                      Summary
                    </h3>
                    <div className="text-sm mt-1">
                      {currentContract.fileName}
                    </div>
                  </div>

                  <div className="bg-white rounded-lg p-3 shadow-sm border border-[#E8E6E1]">
                    <h3 className="text-xs font-medium text-muted-foreground mb-1">
                      Contract Details
                    </h3>
                    <div className="grid grid-cols-2 gap-12 text-xs">
                      <div>
                        <div className="flex justify-between items-center">
                          <span className="text-muted-foreground">Duration:</span>
                          <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100 px-1.5 py-0.5 text-xs">
                            {currentContract.duration}
                          </Badge>
                        </div>
                        <div className="flex justify-between items-center mt-1">
                          <span className="text-muted-foreground">Extendable:</span>
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between items-center">
                          <span className="text-muted-foreground">Start Date:</span>
                          <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100 px-1.5 py-0.5 text-xs">
                            {currentContract.startDate ? formatDate(currentContract.startDate) : "N/A"}
                          </Badge>
                        </div>
                        <div className="flex justify-between items-center mt-1">
                          <span className="text-muted-foreground">End Date:</span>
                          <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100 px-1.5 py-0.5 text-xs">
                            {currentContract.endDate ? formatDate(currentContract.endDate) : "N/A"}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Transition>

      <div className="flex items-center justify-between gap-2 mb-6">
        <div className="mt-4 mb-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => (window.location.href = "/dashboard")}
          >
            <Icons.arrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                disabled={isPresentationLoading || isExcelLoading}
              >
                {(isPresentationLoading || isExcelLoading) ? (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <FileUp className="mr-2 h-4 w-4" />
                )}
                {(isPresentationLoading || isExcelLoading) ? "Generating..." : "Export"}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={handleExportData}
                disabled={isPresentationLoading || isExcelLoading}
              >
                {isPresentationLoading ? (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Icons.download className="mr-2 h-4 w-4" />
                )}
                {isPresentationLoading ? "PowerPoint..." : "Export PowerPoint"}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={handleExportExcel}
                disabled={isPresentationLoading || isExcelLoading}
              >
                {isExcelLoading ? (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <FileSpreadsheet className="mr-2 h-4 w-4" />
                )}
                {isExcelLoading ? "Excel..." : "Export Excel"}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              if (chatOpen && isMinimized) {
                setMinimizedChatAnimation('expand-height');
                setTimeout(() => {
                  setIsMinimized(false);
                  setChatPanelAnimation('slide-up');
                }, 280);
              } else if (chatOpen && !isMinimized) {
                setChatPanelAnimation('slide-down');
                setTimeout(() => {
                  setChatOpen(false);
                }, 280);
              } else {
                setChatOpen(true);
                setIsMinimized(false);
                setChatPanelAnimation('slide-up');
              }
            }}
          >
            <Icons.messageSquare className="mr-2 h-4 w-4" />
            Chat to Contract
          </Button>
          {/* Changed button to accent color for more contrast */}
          <Button variant="outline" size="sm" onClick={handleGlobalRefresh}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Re-run Analysis
          </Button>
          <Button
            size="sm"
            onClick={runAnalysis}
            className="bg-accent hover:bg-accent-dark text-white"
          >
            <Icons.barChart className="mr-2 h-4 w-4" />
            Run Analysis
          </Button>
        </div>
      </div>

      <div className="flex flex-1 relative min-h-0">
        <div
          ref={contentRef}
          className="flex-1 overflow-auto p-4 w-full transition-all duration-300"
        >
          {/* Enhanced Feature Tabs with Icons and Descriptions */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Contract Analysis</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              {/* Transparency Card */}
              {(() => {
                const hasPermission = canViewTransparency(roles) || canViewAdmin(roles);
                return (
                  <div
                    className={`relative rounded-lg p-6 transition-all duration-200 ${analysisTab === "transparency"
                      ? "bg-white shadow-lg border-l-4 border-l-primary"
                      : hasPermission
                        ? "bg-[#F1EEEA]/50 hover:bg-white/80 hover:shadow-md"
                        : "bg-gray-200 cursor-not-allowed"
                      }`}
                    onClick={() => hasPermission && handleTabChange("transparency")}
                  >
                    <div className="flex items-start">
                      <div
                        className={`rounded-full p-3 mr-4 ${analysisTab === "transparency"
                          ? "bg-primary text-white"
                          : hasPermission
                            ? "bg-[#F1EEEA] text-primary"
                            : "bg-gray-300 text-gray-500"
                          }`}
                      >
                        {hasPermission ? <FileText className="h-6 w-6" /> : <Lock className="h-6 w-6" />}
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg mb-1">Contract Transparency</h3>
                        <p className="text-sm text-muted-foreground">
                          Extract key terms and data from your contracts
                        </p>
                      </div>
                    </div>
                    {!hasPermission && (
                      <div className="absolute top-4 right-4 text-gray-500">
                        <Lock className="h-5 w-5" />
                      </div>
                    )}
                  </div>
                );
              })()}
              {/* Benchmarking Card */}
              {(() => {
                const hasPermission = canViewBenchmarking(roles) || canViewAdmin(roles);
                return (
                  <div
                    className={`relative rounded-lg p-6 transition-all duration-200 ${analysisTab === "benchmarking"
                      ? "bg-white shadow-lg border-l-4 border-l-primary"
                      : hasPermission
                        ? "bg-[#F1EEEA]/50 hover:bg-white/80 hover:shadow-md"
                        : "bg-gray-200 cursor-not-allowed"
                      }`}
                    onClick={() => hasPermission && handleTabChange("benchmarking")}
                  >
                    <div className="flex items-start">
                      <div
                        className={`rounded-full p-3 mr-4 ${analysisTab === "benchmarking"
                          ? "bg-primary text-white"
                          : hasPermission
                            ? "bg-[#F1EEEA] text-primary"
                            : "bg-gray-300 text-gray-500"
                          }`}
                      >
                        {hasPermission ? <BarChart className="h-6 w-6" /> : <Lock className="h-6 w-6" />}
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg mb-1">Benchmarking</h3>
                        <p className="text-sm text-muted-foreground">
                          Compare against industry standards
                        </p>
                      </div>
                    </div>
                    {!hasPermission && (
                      <div className="absolute top-4 right-4 text-gray-500">
                        <Lock className="h-5 w-5" />
                      </div>
                    )}
                  </div>
                );
              })()}
              {/* Recommendations Card */}
              {(() => {
                const hasPermission = canViewRecommendations(roles) || canViewAdmin(roles);
                return (
                  <div
                    className={`relative rounded-lg p-6 transition-all duration-200 ${analysisTab === "recommendations"
                      ? "bg-white shadow-lg border-l-4 border-l-primary"
                      : hasPermission
                        ? "bg-[#F1EEEA]/50 hover:bg-white/80 hover:shadow-md"
                        : "bg-gray-200 cursor-not-allowed"
                      }`}
                    onClick={() => hasPermission && handleTabChange("recommendations")}
                  >
                    <div className="flex items-start">
                      <div
                        className={`rounded-full p-3 mr-4 ${analysisTab === "recommendations"
                          ? "bg-primary text-white"
                          : hasPermission
                            ? "bg-[#F1EEEA] text-primary"
                            : "bg-gray-300 text-gray-500"
                          }`}
                      >
                        {hasPermission ? <Lightbulb className="h-6 w-6" /> : <Lock className="h-6 w-6" />}
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg mb-1">Recommendations</h3>
                        <p className="text-sm text-muted-foreground">
                          Optimize costs and contract terms
                        </p>
                      </div>
                    </div>
                    {!hasPermission && (
                      <div className="absolute top-4 right-4 text-gray-500">
                        <Lock className="h-5 w-5" />
                      </div>
                    )}
                  </div>
                );
              })()}
            </div>
          </div>

          <Tabs value={analysisTab} onValueChange={handleTabChange}>
            {(canViewTransparency(roles) || canViewAdmin(roles)) && (
              <TabsContent value="transparency" className="mt-0">
                {/* Contract Transparency Section */}
                {isLoadingExtractionStatistics ? (
                  <div className="flex justify-center items-center p-8">
                    <Icons.spinner className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) :
                  (
                    <TransparencyDashboard
                      dimensions={totalDimensions}
                      totalSubFeatures={extractionStatistics?.total_sub_features || totalSubFeatures}
                      availableSubFeatures={extractionStatistics?.with_data || availableSubFeatures}
                    />
                  )}
                <div className="flex justify-end mb-6 mt-4">
                  <Button variant="outline" size="sm" onClick={handleAddFeature}>
                    <PlusCircle className="mr-2 h-4 w-4" />
                    Extract custom information
                  </Button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {transparencyFeaturesResponse?.custom_features?.map((dimension) => (
                    <TransparencyFeatureCard
                      key={dimension.id}
                      feature={dimension}
                      projectId={projectId}
                      contractId={contractId}
                      handleEditFeature={handleEditFeature}
                      refreshTriggered={refreshCount}
                      isCustom={true}
                    />
                  ))}
                  {transparencyFeaturesResponse?.standard_features?.map((dimension) => (
                    <TransparencyFeatureCard
                      key={dimension.id}
                      feature={dimension}
                      projectId={projectId}
                      contractId={contractId}
                      handleEditFeature={handleEditFeature}
                      refreshTriggered={refreshCount}
                    />
                  ))}
                </div>
              </TabsContent>
            )}
            {(canViewBenchmarking(roles) || canViewAdmin(roles)) && (
              <TabsContent value="benchmarking" className="mt-0">
                {/* Benchmarking Dashboard */}
                {isLoadingBenchmarkStatistics ? (
                  <div className="flex justify-center items-center p-8">
                    <Icons.spinner className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <BenchmarkingDashboard complianceCounts={benchmarkingCounts || { compliant: 0, partially_compliant: 0, non_compliant: 0, not_available: 0 }} />)
                }
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {(evaluationDimensions ?? []).map((dimension) => (
                    <BenchmarkFeatureCard
                      key={dimension.id}
                      feature={dimension}
                      projectId={projectId}
                      contractId={contractId}
                    />
                  ))}
                </div>
              </TabsContent>
            )}
            {(canViewRecommendations(roles) || canViewAdmin(roles)) && (
              <TabsContent value="recommendations" className="mt-0">
                {/* Recommendations Dashboard or overview stats could go here */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {(evaluationDimensions ?? []).map((dimension) => (
                    <RecommendationFeatureCard
                      key={dimension.id}
                      feature={dimension}
                      projectId={projectId}
                      contractId={contractId}
                    />
                  ))}
                </div>
              </TabsContent>
            )}
          </Tabs>
        </div>

        {chatOpen && !isMinimized && !expandedChat && (
          <div
            ref={chatPanelRef}
            className={cn(
              "w-1/3 border-l border-t glassmorphism fixed right-0 bottom-0 h-[60vh] z-10 rounded-tl-lg shadow-lg",
              chatPanelAnimation === 'slide-up' && "animate-slide-up",
              chatPanelAnimation === 'slide-down' && "animate-slide-down",
              chatPanelAnimation === 'zoom-out' && "animate-zoom-out",
              chatPanelAnimation === 'collapse-height' && "animate-collapse-height",
              !chatPanelAnimation && "animate-slide-up"
            )}
          >
            <ChatPanel
              projectId={projectId}
              contractId={contractId}
              onClose={() => {
                setChatPanelAnimation('slide-down');
                setTimeout(() => {
                  setChatOpen(false);
                }, 280);
              }}
              onExpand={() => {
                setChatPanelAnimation('zoom-out');
                setTimeout(() => {
                  setExpandedChat(true);
                }, 280);
              }}
              onMinimize={() => {
                setChatPanelAnimation('collapse-height');
                setTimeout(() => {
                  setIsMinimized(true);
                  setMinimizedChatAnimation('slide-up');
                }, 280);
              }}
            />
          </div>
        )}

        {chatOpen && isMinimized && !expandedChat && (
          <div
            ref={minimizedChatRef}
            className={cn(
              "w-1/3 fixed right-0 bottom-0 z-10 glassmorphism border-t-2 border-l-2 border-primary/20 rounded-tl-lg",
              minimizedChatAnimation === 'slide-up' && "animate-slide-up",
              minimizedChatAnimation === 'slide-down' && "animate-slide-down",
              minimizedChatAnimation === 'expand-height' && "animate-expand-height",
              !minimizedChatAnimation && "animate-slide-up"
            )}
          >
            <MinimizedChatBar
              onExpand={() => {
                setTimeout(() => {
                  setIsMinimized(false);
                  setChatPanelAnimation('slide-up');
                  setChatOpen(true);
                }, 100);
              }}
              onClose={() => {
                setTimeout(() => {
                  setMinimizedChatAnimation('slide-down');
                  setChatOpen(false);
                }, 280);
              }}
            />
          </div>
        )}
      </div>

      <Dialog open={expandedChat} onOpenChange={setExpandedChat}>
        <DialogContent
          ref={dialogContentRef}
          className={cn(
            "max-w-5xl w-full h-[80vh] overflow-hidden p-0 pt-0 mt-0 data-[state=open]:translate-y-[-45%]",
            dialogAnimation === 'zoom-in' && "animate-zoom-in",
            dialogAnimation === 'zoom-out' && "animate-zoom-out",
            !dialogAnimation && "animate-zoom-in"
          )}
          showCloseButton={false}
        >
          <ChatPanel
            projectId={projectId}
            contractId={contractId}
            onClose={() => {
              setDialogAnimation('zoom-out');
              setTimeout(() => {
                setExpandedChat(false);
                setChatOpen(false);
              }, 280);
            }}
            onMinimize={() => {
              setDialogAnimation('zoom-out');
              setTimeout(() => {
                setExpandedChat(false);
                setChatOpen(true);
                setIsMinimized(true);
                setMinimizedChatAnimation('slide-up');
              }, 280);
            }}
            isExpanded={true}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={featureModalOpen} onOpenChange={setFeatureModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingFeature ? "Edit Custom Feature" : "Create Custom Feature"}
            </DialogTitle>
            <DialogDescription>
              {editingFeature
                ? "Update your custom transparency feature."
                : "Create a new custom transparency feature to extract specific information from your contracts."}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSaveFeature}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">
                  Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  name="name"
                  defaultValue={editingFeature?.feature_name || ""}
                  placeholder="e.g., Payment Terms Analysis"
                  required
                />
              </div>


              <div className="grid gap-2">

                <Label htmlFor="question">Question <span className="text-red-500">*</span></Label>
                <Input
                  id="question"
                  name="question"
                  defaultValue={editingFeature?.extraction_question || ""}
                  placeholder="Enter the question to extract information"
                  required
                />
              </div >


              <div className="grid gap-2">
                <Label htmlFor="generationInstruction">Generation Instruction</Label>
                <Textarea
                  id="generationInstruction"
                  name="generationInstruction"
                  defaultValue={editingFeature?.generation_instruction || ""}
                  placeholder="Optional generation instructions (e.g., 'Extract payment terms and conditions from the contract.')"
                  className="min-h-[100px]"
                />
              </div >
            </div >
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setFeatureModalOpen(false);
                  setEditingFeature(null);
                }}
                disabled={isCreatingFeature}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isCreatingFeature}>
                {isCreatingFeature
                  ? "Creating Feature..."
                  : editingFeature
                    ? "Update Feature"
                    : "Create Feature"}
              </Button>
            </DialogFooter>
          </form >
        </DialogContent >
      </Dialog >
      <ToastContainer />
    </div >
  );
}

function calculateDuration(start_date: any, end_date: any) {
  if (start_date && end_date) {
    const start = parseISO(start_date);
    const end = parseISO(end_date);
    const totalMonths = differenceInMonths(end, start);
    const years = Math.floor(totalMonths / 12);
    const months = totalMonths % 12;

    let result = '';
    if (years > 0) {
      result += `${years} year${years !== 1 ? 's' : ''}`;
    }
    if (months > 0) {
      if (result) result += ' and ';
      result += `${months} month${months !== 1 ? 's' : ''}`;
    }

    return result || '0 months'; // fallback in case duration is 0
  }
  return null;
}