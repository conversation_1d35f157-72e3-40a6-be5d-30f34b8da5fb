"use client";

import type React from "react";

import { useEffect, useMemo, useState, useCallback, useRef } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
// import { getProject } from "@/lib/data";
import { formatBytes, formatDate } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Icons } from "@/components/icons";
import { toast, ToastContainer } from "react-toastify";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { CheckCircle2, XCircle, Clock, AlertCircle } from "lucide-react"
import { useGetDocuments, useTriggerIngestion } from "@/api/document"
import { useProjects } from "@/api/projects"
import { useFileAnalysisDeduplication } from "@/api/file-analysis"
import { useProjectFileStore } from "@/store/project-file-store"
import {
  processFiles as processUploadedFiles,
  handleDragEnter as dragEnter,
  handleDragLeave as dragLeave,
  handleDragOver as dragOver,
  handleFileDrop as fileDrop,
  handleFileInputChange as fileInputChange
} from "@/utils/file-upload";
import { FileUploadArea } from "@/components/FileUpload";
import { queryClient } from "@/api/query-client";

export default function FileManagementPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.project_id as string;
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [uploadStatus, setUploadStatus] = useState<Record<string, "pending" | "success" | "error">>({});
  const [isDragging, setIsDragging] = useState(false);
  const {
    mutate: triggerIngestion,
    isPending: isIngesting
  } = useTriggerIngestion();
  const fileInputRef = useRef<HTMLInputElement>(null) as React.RefObject<HTMLInputElement>;
  const folderInputRef = useRef<HTMLInputElement>(null) as React.RefObject<HTMLInputElement>;
  const { mutate: analyzeFileDeduplication, isPending: isAnalyzing } = useFileAnalysisDeduplication();
  const setFilesData = useProjectFileStore((state) => state.setFiles);
  const setDeduplicationData = useProjectFileStore((state) => state.setDeduplicationData);
  const setProjectId = useProjectFileStore((state) => state.setProjectId);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false)
  const { data: userProjects } = useProjects();
  const { data: documents } = useGetDocuments(projectId);
  const [prevFileStatuses, setPrevFileStatuses] = useState<
    Record<string, string>
  >({});

  const docs = useMemo(() => {
    return documents?.length
      ? documents.map((doc) => ({
        id: doc.id,
        name: doc.name,
        contract_id: doc.contract_id,
        project_id: doc.project_id,
        fileCategory: doc.document_type,
        fileType: doc.name,
        size: doc.document_size,
        uploadedAt: doc.uploaded_at,
        status:
          doc.status === "PROCESSED"
            ? "processed"
            : doc.status === "PENDING"
              ? "uploaded"
              : doc.status === "IDLE"
                ? "idle"
                : "error",
      }))
      : [];
  }, [documents]);

  // Determine if any files need reprocessing
  const hasPendingOrIdle = docs.some(doc => ["uploaded", "idle", "error"].includes(doc.status));

  useEffect(() => {
    if (!documents || !documents.length) return;

    const newStatuses: Record<string, string> = {};

    documents.forEach((file) => {
      const currentStatus = file.status;
      const prevStatus = prevFileStatuses[file.id] ?? file.status;

      newStatuses[file.id] = currentStatus;

      if (prevStatus !== currentStatus) {
        if (currentStatus === "PROCESSED") {
          if (prevStatus === "PENDING") {
            toast.success(
              `File "${file.name}" has been processed successfully.`
            );
          } else {
            toast.success(`File "${file.name}" is already processed.`);
          }
        } else if (currentStatus === "ERROR") {
          toast.error(`Error processing file "${file.name}".`);
        }
      }
    });


    setPrevFileStatuses(newStatuses);
  }, [documents, projectId, router]);

  const userProject = userProjects?.find((project) => project.id === projectId);

  const currentProject = {
    id: userProject?.id || projectId,
    name: userProject?.name || "Loading project...",
    clientName: userProject?.client || "Loading client...",
    caseCode: userProject?.case_code || "N/A",
    description: userProject?.description || "",
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "uploaded":
        return <Clock className="h-5 w-5 text-[#FFE68F]" />;
      case "processed":
        return <CheckCircle2 className="h-5 w-5 text-[#21BF61]" />;
      case "error":
        return <XCircle className="h-5 w-5 text-[#D82216]" />;
      case "idle":
        return <AlertCircle className="h-5 w-5 text-[#9CA3AF]" />;
      default:
        return null;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "uploaded":
        return "Pending";
      case "processed":
        return "Processed";
      case "error":
        return "Error";
      case "idle":
        return "Idle";
      default:
        return "";
    }
  };

  // We're now using the imported simulateFileUpload function directly

  // Process files (add to state and simulate upload)
  const processFiles = useCallback(
    (fileArray: File[]) => {
      // Use the imported function but adapt it to our component's state
      processUploadedFiles(
        fileArray,
        setSelectedFiles,
        uploadProgress,
        setUploadProgress,
        uploadStatus,
        setUploadStatus
      );
    },
    [uploadProgress, uploadStatus]
  )

  const handleDragEnter = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    dragEnter(e, setIsDragging);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    dragLeave(e, setIsDragging);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    dragOver(e, isDragging, setIsDragging);
  }, [isDragging]);

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    fileDrop(e, setIsDragging, processFiles);
  }, [processFiles]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    fileInputChange(e, processFiles);
  }, [processFiles]);

  const handleConfirmUpload = (project_id: string) => async () => {
    if (selectedFiles.length === 0) {
      toast.error("No files to analyze.");
      return;
    }

    const formData = new FormData();
    selectedFiles.forEach((file) => {
      const relativePath = file.webkitRelativePath || file.name;
      formData.append("files", file, relativePath);
    });

    if (formData.getAll("files").length === 0) {
      toast.error("No files were added to the upload. Please try again.");
      return;
    }

    setFilesData(selectedFiles.map(file => ({
      file,
      relativePath: file.webkitRelativePath || file.name,
    })));

    // Store project ID in Zustand
    setProjectId(project_id);

    try {
      analyzeFileDeduplication({projectId: project_id, files: formData}, {
        onSuccess: (deduplicationResponse) => {
          setDeduplicationData(deduplicationResponse);
          router.push(`/project/${project_id}/deduplication-review`);
        },
        onError: (error) => {
          console.error("Error in Analyzing Files:", error);
          toast.error(`Error in Analyzing Files: ${error.message}`);
        },
      });
    } catch (error) {
      console.error("Unexpected error:", error);
      toast.error(`Unexpected error: ${error}`);
    }
  };

  // Remove a file from the list
  const removeFile = (identifier: string) => {
    setSelectedFiles((prev) => prev.filter((f) => (f.webkitRelativePath || f.name) !== identifier));

    // Also remove from progress and status
    const newProgress = { ...uploadProgress };
    const newStatus = { ...uploadStatus };
    delete newProgress[identifier];
    delete newStatus[identifier];

    setUploadProgress(newProgress);
    setUploadStatus(newStatus);
  };

  // We're now using the removeFile function directly

  return (
    <div className="flex h-full flex-col">
      <div className="mb-6">
        <div className="bg-gradient-to-r from-white to-[#F8F7F4] rounded-lg shadow-md border border-[#E8E6E1]">
          <div className="p-6">
            <div className="flex flex-col gap-5">
              <div className="flex justify-between">
                <div className="flex gap-5">
                  <div className="flex-shrink-0 bg-primary/10 rounded-lg p-4 flex items-center justify-center">
                    <Icons.fileContract className="h-8 w-8 text-primary" />
                  </div>
                  <div>
                    <div className="flex items-center gap-3 mb-1">
                      <h1 className="text-2xl font-bold">
                        {currentProject.name}
                      </h1>
                      <span className="px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#E9F7F2] text-[#096A54]">
                        Active
                      </span>
                    </div>
                    <p className="text-[#2E5CFA] text-lg font-semibold">
                      {currentProject.clientName}
                    </p>
                    <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1.5">
                        <Icons.tag className="h-3.5 w-3.5" />
                        <span>Case Code: {currentProject.caseCode}</span>
                      </div>
                      <div className="flex items-center gap-1.5">
                        <Icons.calendar className="h-3.5 w-3.5" />
                        <span>Created: Apr 10, 2023</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Link href={`/project/${projectId}/file-management`} passHref>
                    <Button variant="outline" size="sm" className="h-9">
                      <Icons.database className="mr-2 h-4 w-4" />
                      Manage Data
                    </Button>
                  </Link>
                  <Button variant="outline" size="sm" className="h-9">
                    <Icons.share className="mr-2 h-4 w-4" />
                    Share
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-2">
                <div className="bg-white rounded-lg p-4 shadow-sm border border-[#E8E6E1]">
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    Files Overview
                  </h3>
                  <p className="text-sm">
                    Manage contract documents, exhibits, schedules and other
                    related files.
                  </p>
                </div>

                <div className="bg-white rounded-lg p-4 shadow-sm border border-[#E8E6E1]">
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    File Management
                  </h3>
                  <p className="text-sm">
                    Upload, organize, and categorize contract-related documents.
                  </p>
                </div>

                <div className="bg-white rounded-lg p-4 shadow-sm border border-[#E8E6E1]">
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    Processing Status
                  </h3>
                  <p className="text-sm">
                    {docs.filter((doc) => doc.status === "processed").length} of{" "}
                    {docs.length} files processed
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex items-center justify-between gap-2 mb-6">
        <Link href={`/project/${projectId}`} passHref>
          <Button variant="outline" size="sm">
            <Icons.arrowLeft className="mr-2 h-4 w-4" />
            Back to Project
          </Button>
        </Link>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setUploadDialogOpen(true)}
          >
            <Icons.upload className="mr-2 h-4 w-4" />
            Upload Files
          </Button>
          <Button
            variant="outline"
            size="sm"
            disabled={!hasPendingOrIdle || isIngesting}
            onClick={() => {
              triggerIngestion(projectId, {
                onSuccess: () => {
                  queryClient.invalidateQueries({ queryKey: ["documents", projectId] });
                  toast.info("Re-running analysis on pending files...");
                },
                onError: (error) => {
                  toast.error(`Error re-running analysis: ${error?.message || "Unknown error"}`);
                },
              });
            }}
          >
            {isIngesting ? <Icons.spinner className="mr-2 h-4 w-4 animate-spin" /> : null}
            Re-run Ingestion
          </Button>
        </div>
      </div>

      <div className="space-y-8">
        {/* Contract Files Section */}
        <div>
          <h2 className="text-xl font-semibold mb-4">Contract Files</h2>
          <div className="rounded-lg border bg-card shadow-sm">
            <div className="max-h-[400px] overflow-y-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>File Category</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Size</TableHead>
                    <TableHead>Uploaded</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {docs.map((file) => (
                    <TableRow key={file.id}>
                      <TableCell className="font-medium">{file.name}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="capitalize">
                          {file.fileCategory}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {file.fileType.includes("pdf")
                          ? "PDF"
                          : file.fileType.includes("sheet")
                            ? "Excel"
                            : "Word"}
                      </TableCell>
                      <TableCell>{formatBytes(file.size)}</TableCell>
                      <TableCell>{formatDate(file.uploadedAt)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(file.status)}
                          <span>{getStatusText(file.status)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {file.fileCategory === "CONTRACT" && (
                          <Button
                            className="bg-[#21BF61] hover:bg-[#21BF61]/90"
                            disabled={
                              file.status !== "processed" ||
                              docs.some((doc) => doc.status !== "processed")
                            }
                            onClick={() =>
                              router.push(`/project/${file.project_id}/contract/${file.contract_id}`)
                            }
                          >
                            View Analysis
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-2">File Categories</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Manage and organize your contract file categories
              </p>
              <Button variant="outline" size="sm" className="w-full">
                <Icons.folderPlus className="mr-2 h-4 w-4" />
                Manage Categories
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-2">Batch Processing</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Process multiple files at once
              </p>
              <Button variant="outline" size="sm" className="w-full">
                <Icons.layers className="mr-2 h-4 w-4" />
                Batch Process
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-2">Export Data</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Export contract data and metadata
              </p>
              <Button variant="outline" size="sm" className="w-full">
                <Icons.download className="mr-2 h-4 w-4" />
                Export
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
        <DialogContent className="w-[90vw] max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Upload Files</DialogTitle>
            <DialogDescription>
              Select files and folders below, then click "Upload" to send them to the server
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <FileUploadArea
              fileInputRef={fileInputRef}
              folderInputRef={folderInputRef}
              isDragging={isDragging}
              isProcessing={isAnalyzing}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              onFileInputChange={handleFileSelect}
              files={selectedFiles}
              uploadProgress={uploadProgress}
              uploadStatus={uploadStatus}
              removeFile={removeFile}
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setUploadDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirmUpload(projectId)}
              disabled={isAnalyzing || selectedFiles.length === 0}
            >
              {isAnalyzing ? (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              ) : null}
              {isAnalyzing ? "Analyzing files..." : "Analyze files"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <ToastContainer />
    </div>
  );
}
