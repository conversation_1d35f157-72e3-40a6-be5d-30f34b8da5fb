"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { ChevronRight, LineChart, FileText, Zap } from "lucide-react"
import { signIn, signOut, useSession } from "next-auth/react";


export default function LandingPage() {
  const router = useRouter()
  const [createProjectOpen, setCreateProjectOpen] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const { data: session, status } = useSession();
  const loading = status === 'loading';


  const handleCreateProject = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsCreating(true)

    // Simulate project creation
    setTimeout(() => {
      setIsCreating(false)
      setCreateProjectOpen(false)
      router.push("/project/1")
    }, 1500)
  }

  const handleViewProjects = () => {
    router.push("/dashboard")
  }

  const navigateToCreateProject = () => {
    router.push("/create-project")
  }

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      {/* Header */}
        <header className="container mx-auto px-4 py-6 flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <div className="bg-primary rounded-md p-2">
              <LineChart className="h-6 w-6 text-primary-foreground" />
            </div>
            <span className="font-bold text-xl">BCG Cost.AI</span>
          </div>
          {loading ? (
            <Button variant="outline" disabled>
              Loading…
            </Button>
          ) : session?.user ? (
            <Button variant="outline" onClick={() => signOut()}>
              Log Out
            </Button>
          ) : (
            <Button variant="outline" onClick={() => signIn('okta', { callbackUrl: '/dashboard' })}>
              Log In
            </Button>
          )}
        </header>

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-20 md:py-32 flex flex-col items-center text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-3xl"
        >
          <h1 className="text-4xl md:text-6xl font-bold tracking-tight mb-6">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-[#21BF61]">
              Uncover savings.
            </span>{" "}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#21BF61] to-[#FFE68F]">
              Upgrade sourcing strategy.
            </span>
          </h1>
          <p className="text-xl text-gray-600 mb-10 max-w-2xl mx-auto">
            Analyze IT contracts with AI-powered insights to optimize costs and enhance service levels.
          </p>
        </motion.div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-4 py-20">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.h2 variants={itemVariants} className="text-3xl font-bold mb-4">
            Powerful Contract Analysis
          </motion.h2>
          <motion.p variants={itemVariants} className="text-xl text-gray-600 max-w-2xl mx-auto">
            Our AI-powered platform transforms complex contracts into actionable insights
          </motion.p>
        </motion.div>

        <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {/* Feature 1 */}
          <motion.div
            variants={itemVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="bg-white rounded-xl p-8 shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300 hover:scale-105 hover:border-primary/20"
          >
            <div className="bg-blue-50 rounded-full w-20 h-20 flex items-center justify-center mb-6 mx-auto">
              <FileText className="h-10 w-10 text-blue-600" />
            </div>
            <h3 className="text-xl font-semibold mb-3">Extract Data</h3>
            <p className="text-gray-600">
              Our AI automatically extracts key contract terms, SLAs, pricing models, and obligations from complex
              documents.
            </p>
          </motion.div>

          {/* Feature 2 */}
          <motion.div
            variants={itemVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-xl p-8 shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300 hover:scale-105 hover:border-primary/20"
          >
            <div className="bg-green-50 rounded-full w-20 h-20 flex items-center justify-center mb-6 mx-auto">
              <LineChart className="h-10 w-10 text-green-600" />
            </div>
            <h3 className="text-xl font-semibold mb-3">Benchmark</h3>
            <p className="text-gray-600">
              Compare your contract terms against industry standards to identify cost inefficiencies and improvement
              opportunities.
            </p>
          </motion.div>

          {/* Feature 3 */}
          <motion.div
            variants={itemVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-xl p-8 shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300 hover:scale-105 hover:border-primary/20"
          >
            <div className="bg-amber-50 rounded-full w-20 h-20 flex items-center justify-center mb-6 mx-auto">
              <Zap className="h-10 w-10 text-amber-600" />
            </div>
            <h3 className="text-xl font-semibold mb-3">Generate Recommendations</h3>
            <p className="text-gray-600">
              Receive actionable recommendations to optimize costs, improve terms, and enhance service levels with clear
              implementation steps.
            </p>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-4 py-20">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="bg-gradient-to-r from-primary/10 to-[#21BF61]/10 rounded-2xl p-10 md:p-16 text-center max-w-5xl mx-auto"
        >
          <h2 className="text-3xl font-bold mb-6">Ready to optimize your IT contracts?</h2>
          <p className="text-xl text-gray-600 mb-10 max-w-2xl mx-auto">
            Start analyzing your contracts today and uncover savings typically ranging from 15% to 30%.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              className="bg-primary hover:bg-primary/90 text-white font-semibold px-8"
              onClick={navigateToCreateProject}
            >
              Create a project
              <ChevronRight className="ml-2 h-4 w-4" />
            </Button>
            <Button size="lg" variant="outline" className="font-semibold px-8" onClick={handleViewProjects}>
              View existing projects
            </Button>
          </div>
        </motion.div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-50 border-t border-gray-100 py-12">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <div className="bg-primary rounded-md p-1.5">
                <LineChart className="h-5 w-5 text-primary-foreground" />
              </div>
              {/* Changed from "COLA - IT Cost Takeout" to "BCG Cost.AI" */}
              <span className="font-bold">BCG Cost.AI</span>
            </div>
            <div className="text-sm text-gray-500">© {new Date().getFullYear()} BCG. All rights reserved.</div>
          </div>
        </div>
      </footer>
    </div>
  )
}
