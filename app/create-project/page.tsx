"use client"

import type React from "react"

import { useState, useCallback, useRef, useMemo } from "react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Upload } from "lucide-react"
import { useCreateProject } from "@/api/projects"
import { useFileAnalysisDeduplication } from "@/api/file-analysis"
import { toast, ToastContainer } from "react-toastify"
import { useProjectFileStore } from "@/store/project-file-store"
import {
  organizeFilesByFolder,
  processFiles as processUploadedFiles,
  handleDragEnter as dragEnter,
  handleDragLeave as dragLeave,
  handleDragOver as dragOver,
  handleFileDrop as fileDrop,
  handleFileInputChange as fileInputChange
} from "@/utils/file-upload"
import FolderView from "@/components/FolderView"
import {ALLOWED_FILE_EXTENSIONS} from "@/src/config/documentExtensions";
import {FileUploadArea} from "@/components/FileUpload";

export default function CreateProjectPage() {
  const router = useRouter()
  const [isDragging, setIsDragging] = useState(false)
  const [files, setFiles] = useState<File[]>([])
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({})
  const [uploadStatus, setUploadStatus] = useState<Record<string, "pending" | "success" | "error">>({})
  const folderInputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [isProjectProcessing, setIsProjectProcessing] = useState(false)

  const { mutate: createProject, isPending: isCreatingProject } = useCreateProject();
  const { mutate: analyzeFileDeduplication, isPending: isAnalyzing } = useFileAnalysisDeduplication();

  const setDeduplicationData = useProjectFileStore((state) => state.setDeduplicationData);
  const setFilesData = useProjectFileStore((state) => state.setFiles);
  const setProjectId = useProjectFileStore((state) => state.setProjectId); // Access setProjectId from Zustand

  const folderStructure = useMemo(() => organizeFilesByFolder(files), [files]);

  // Use the imported utility functions with our component's state
  const handleDragEnter = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    dragEnter(e, setIsDragging);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    dragLeave(e, setIsDragging);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    dragOver(e, isDragging, setIsDragging);
  }, [isDragging]);

  // Process dropped or selected files
  const processFiles = useCallback(
    (fileArray: File[]) => {
      // Use the imported function but adapt it to our component's state
      processUploadedFiles(
        fileArray,
        setFiles,
        uploadProgress,
        setUploadProgress,
        uploadStatus,
        setUploadStatus
      );
    },
    [uploadProgress, uploadStatus]
  );

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    fileDrop(e, setIsDragging, processFiles);
  }, [processFiles]);

  // Handle file input change
  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    fileInputChange(e, processFiles);
  }, [processFiles]);

  // We're now using the imported simulateFileUpload function directly

  // Remove a file from the list
  const removeFile = (fileName: string) => {
    setFiles((prev) => prev.filter((file) => file.name !== fileName))

    // Also remove from progress and status
    const newProgress = { ...uploadProgress }
    const newStatus = { ...uploadStatus }
    delete newProgress[fileName]
    delete newStatus[fileName]

    setUploadProgress(newProgress)
    setUploadStatus(newStatus)
  }

  const handleConfirmUpload = async (project_id: string) => {
    if (files.length === 0) {
      toast.error("No files to analyze.");
      return;
    }

    const formData = new FormData();
    files.forEach((file) => {
      const relativePath = file.webkitRelativePath || file.name;
      formData.append("files", file, relativePath);
    });

    // Store files in Zustand
    setFilesData(files.map(file => ({
      file,
      relativePath: file.webkitRelativePath || file.name,
    })));

    try {
      analyzeFileDeduplication({projectId: project_id, files: formData}, {
        onSuccess: (deduplicationResponse) => {
          setDeduplicationData(deduplicationResponse);
          router.push(`/project/${project_id}/deduplication-review`);
        },
        onError: (error) => {
          console.error("Error in Analyzing Files:", error);
          toast.error(`Error in Analyzing Files: ${error.message}`);
        },
      });
    } catch (error) {
      console.error("Unexpected error:", error);
      toast.error(`Unexpected error: ${error}`);
    }
  };

  const handleCreateProject = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    setIsProjectProcessing(true)

    // Access the input elements using their ids
    const nameInput = e.currentTarget.elements.namedItem('name') as HTMLInputElement;
    const name = nameInput.value;

    const clientInput = e.currentTarget.elements.namedItem('client') as HTMLInputElement;
    const client = clientInput.value;

    const caseCodeInput = e.currentTarget.elements.namedItem('caseCode') as HTMLInputElement;
    const caseCode = caseCodeInput.value;

    const descriptionInput = e.currentTarget.elements.namedItem('description') as HTMLTextAreaElement;
    const description = descriptionInput.value;

    createProject(
      {
        name: name,
        client: client,
        case_code: caseCode,
        description: description,
      },
      {
        onSuccess: (project_id: string) => {
          setProjectId(project_id); // Store project_id in Zustand
          toast.success("Project set-up successfully!");
          handleConfirmUpload(project_id);
        },
        onError: (error) => {
          console.error("Error creating project:", error);
          toast.error(`Error creating project: ${error.message}`);

        },
      }
    );
  };



  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 py-12">
      <div className="container mx-auto px-4">
        <Button variant="ghost" className="mb-8" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>

        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
          <h1 className="text-3xl font-bold mb-3">Unlock Contract Value</h1>
          <p className="text-gray-600 text-lg mb-8">
            Upload your client's contracts to extract insights, benchmark against industry standards, and generate
            optimization recommendations.
          </p>

          <form onSubmit={handleCreateProject} className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Project Details */}
              <div className="space-y-6">
                <h2 className="text-xl font-semibold">Project Details</h2>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name" className="text-base">
                      Project Name
                    </Label>
                    <Input id="name" placeholder="Enter project name" className="mt-1.5" required />
                  </div>
                  <div>
                    <Label htmlFor="client" className="text-base">
                      Client Name
                    </Label>
                    <Input id="client" placeholder="Enter client name" className="mt-1.5" required />
                  </div>
                  <div>
                    <Label htmlFor="caseCode" className="text-base">
                      Case Code
                    </Label>
                    <Input
                      id="caseCode"
                      placeholder="e.g., 12102-25"
                      className="mt-1.5"
                    />
                  </div>

                  <div>
                    <Label htmlFor="description" className="text-base">
                      Description
                    </Label>
                    <Textarea
                      id="description"
                      placeholder="Enter project description"
                      className="mt-1.5 min-h-[120px]"
                    />
                  </div>
                </div>
              </div>

              {/* File Upload */}
              <div className="space-y-6">
                <h2 className="text-xl font-semibold">Upload Contract Files</h2>
                <FileUploadArea
                  fileInputRef={fileInputRef}
                  folderInputRef={folderInputRef}
                  isDragging={isDragging}
                  isProcessing={isProjectProcessing}
                  onDragEnter={handleDragEnter}
                  onDragLeave={handleDragLeave}
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                  onFileInputChange={handleFileInputChange}
                  files={files}
                  uploadProgress={uploadProgress}
                  uploadStatus={uploadStatus}
                  removeFile={removeFile}
                />
              </div>
            </div>

            <div className="flex justify-end gap-4 pt-4">
              <Button
                type="submit"
                size="lg"
                disabled={isProjectProcessing || files.length === 0}
                className="px-8"
              >
                {isProjectProcessing ? (
                  <>
                    <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-primary-foreground border-t-transparent"></span>
                    Analyzing files...
                  </>
                ) : (
                  "Analyze files"
                )}
              </Button>
            </div>
          </form>
        </motion.div>
      </div>
      <ToastContainer />
    </div>
  );
}

