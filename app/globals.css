@tailwind base;
@tailwind components;
@tailwind utilities;

/* Font imports would typically go here - assuming BCG Henderson fonts are available */
@font-face {
  font-family: "BCG Henderson Mod";
  src: url("/fonts/BCGHendersonMod-Regular.woff2") format("woff2");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "BCG Henderson Mod";
  src: url("/fonts/BCGHendersonMod-Bold.woff2") format("woff2");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "BCG Henderson Sans";
  src: url("/fonts/BCGHendersonSans-Regular.woff2") format("woff2");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "BCG Henderson Sans";
  src: url("/fonts/BCGHendersonSans-Bold.woff2") format("woff2");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@layer base {
  :root {
    /* Primary Colors - Teal */
    --primary: 174 100% 50%;
    --primary-light: 165 100% 50%;
    --primary-medium: 169 100% 44%;
    --primary-shade: 169 100% 33%;
    --primary-dark: 175 97% 24%;
    --primary-foreground: 0 0% 100%;

    /* Secondary Colors - Cerulean */
    --secondary: 180 80% 49%;
    --secondary-light: 180 85% 73%;
    --secondary-medium: 180 80% 49%;
    --secondary-dark: 184 100% 30%;
    --secondary-foreground: 240 5% 12%;

    /* Accent Colors - Cobalt */
    --accent: 252 100% 58%;
    --accent-light: 237 94% 71%;
    --accent-medium: 252 100% 58%;
    --accent-dark: 252 87% 33%;
    --accent-foreground: 0 0% 100%;

    /* Neutral Colors */
    --background: 0 0% 100%;
    --foreground: 240 4% 14%;

    --muted: 240 10% 92%;
    --muted-foreground: 240 2% 41%;

    --card: 0 0% 100%;
    --card-foreground: 240 4% 14%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 4% 14%;

    --border: 240 10% 92%;
    --input: 240 10% 92%;
    --ring: 169 100% 44%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-henderson-sans;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-henderson-mod font-bold;
  }

  h1 {
    @apply text-3xl md:text-4xl;
  }

  h2 {
    @apply text-2xl md:text-3xl;
  }

  h3 {
    @apply text-xl md:text-2xl;
  }

  h4 {
    @apply text-lg md:text-xl;
  }

  p,
  span,
  div,
  li,
  a {
    @apply font-henderson-sans;
  }

  button,
  .button {
    @apply font-henderson-sans font-medium;
  }
}

/* BCG X specific component styling */
@layer components {
  .bcg-card {
    @apply bg-white rounded-lg border border-border shadow-sm hover:shadow-md transition-shadow;
  }

  .bcg-button-primary {
    @apply bg-primary text-white hover:bg-primary-shade transition-colors;
  }

  .bcg-button-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary-dark hover:text-white transition-colors;
  }

  .bcg-button-accent {
    @apply bg-accent text-white hover:bg-accent-dark transition-colors;
  }

  .bcg-gradient-bg {
    @apply bg-gradient-to-r from-primary to-secondary;
  }
}
