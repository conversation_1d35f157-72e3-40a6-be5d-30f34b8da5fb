"use client"

import type React from "react"

import { SessionProvider } from "next-auth/react"
import { useEffect } from "react"
import { useToast } from "@/components/ui/use-toast"
import { queryClient } from "@/api/query-client"
import { QueryClientProvider } from "@tanstack/react-query"

export function Providers({ children }: { children: React.ReactNode }) {
  const { toast } = useToast()

  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      console.error("Unhandled error:", event.error)
      toast({
        title: "An error occurred",
        description: "Please try again later or contact support if the problem persists.",
        variant: "destructive",
      })
    }

    window.addEventListener("error", handleError)

    return () => {
      window.removeEventListener("error", handleError)
    }
  }, [toast])


  return (
    <SessionProvider>
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    </SessionProvider>
  );
}
