import {getToken} from "next-auth/jwt";
import {NextResponse} from "next/server";
import {
    ROLE_ADMIN,
    ROLE_BENCHMARKING,
    ROLE_RECOMMENDATIONS,
    ROLE_TRANSPARENCY
} from "@/src/config/roles";

export async function middleware(req: any) {
    // 1) <PERSON>rab and verify the JWT from the incoming request
    const token = await getToken({req, secret: process.env.NEXTAUTH_SECRET});

    if (!token) {
        // No valid JWT → redirect to sign-in
        return NextResponse.redirect(new URL("/login", req.url));
    }

    if (Date.now() >= (token.accessTokenExpires as number)) {
        // Redirect back through NextAuth so it can refresh
        return NextResponse.redirect(new URL("/api/auth/signin", req.url));
    }
    // 2) Enforce additional claims
    // e.g. require roles
    const allowedRoles = [
        ROLE_ADMIN,
        ROLE_BENCHMARKING,
        ROLE_RECOMMENDATIONS,
        ROLE_TRANSPARENCY
    ];
    const userRoles = Array.isArray(token.role) ? token.role : [token.role];
    if (!userRoles.some(role => allowedRoles.includes(role))) {
        return new NextResponse('Forbidden', {status: 403});
    }

    // 3) All good → continue on
    return NextResponse.next();
}

export const config = {
    matcher: [
        "/((?!api/auth|_next/static|_next/image|favicon.ico|login).+)"
    ],
};