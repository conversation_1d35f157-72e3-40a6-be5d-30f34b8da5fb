# IT Cost Takeout Analysis Tool

A web application to analyze contract data for cost takeout opportunities. This tool helps identify potential savings in IT contracts through detailed analysis of SLAs, pricing models, and other contract dimensions.


## Features

- **Contract Analysis**: Upload and analyze IT contracts to identify cost-saving opportunities
- **Multi-dimensional Evaluation**: Analyze contracts across multiple dimensions including SLAs, penalty clauses, productivity gains, and more
- **Custom Extraction**: Create custom extraction features to analyze specific aspects of contracts
- **Benchmarking**: Compare contract terms against industry standards and best practices
- **Recommendations**: Get actionable recommendations for cost optimization.
- **Interactive Chat**: Chat with an AI assistant to get insights about your contracts

## Getting Started

### Prerequisites

- Node.js 18.x or higher
- yarn

### Installation

1. Clone the repository:

```bash
git clone https://github.com/your-username/it-cost-takeout.git
cd it-cost-takeout
```

2. Install dependencies:

```bash
yarn install
```

### Running the Development Server

```bash
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the application.

### Building for Production

```bash
yarn build
```

To start the production server:

```bash
yarn start
```

## Project Structure

```
it-cost-takeout/
├── app/                    # Next.js App Router
│   ├── (dashboard)/        # Dashboard routes
│   │   ├── dashboard/      # Dashboard page
│   │   ├── project/        # Project pages
│   │   └── analysis/       # Analysis pages
│   ├── layout.tsx          # Root layout
│   ├── page.tsx            # Root page (redirects to dashboard)
│   └── providers.tsx       # App providers
├── components/             # React components
│   ├── ui/                 # UI components (shadcn/ui)
│   ├── contract-transparency.tsx  # Contract transparency component
│   ├── benchmarking-result.tsx    # Benchmarking result component
│   ├── recommendation-card.tsx    # Recommendation card component
│   └── ...                 # Other components
├── lib/                    # Utility functions and data
│   ├── data.ts             # Mock data for projects and files
│   └── utils.ts            # Utility functions
├── styles/                 # Global styles
│   └── globals.css         # Global CSS with Tailwind directives
├── types/                  # TypeScript type definitions
│   └── index.ts            # Type definitions
├── public/                 # Static assets
├── next.config.js          # Next.js configuration
├── tailwind.config.js      # Tailwind CSS configuration
├── tsconfig.json           # TypeScript configuration
└── package.json            # Project dependencies and scripts
```

## Technologies Used

- **Next.js**: React framework for server-rendered applications
- **React**: JavaScript library for building user interfaces
- **TypeScript**: Typed superset of JavaScript
- **Tailwind CSS**: Utility-first CSS framework
- **shadcn/ui**: Reusable UI components built with Radix UI and Tailwind CSS
- **Lucide React**: Icon library

## Custom Features

### Adding Custom Extraction Features

1. Navigate to the Contract Transparency tab
2. Click "Extract custom information"
3. Enter a name and prompt for the extraction
4. The custom feature will appear at the top of the list
5. You can edit or delete custom features as needed

### Contract Analysis

The application analyzes contracts across multiple dimensions:
- SLA Benchmarking
- Penalty & Breach Clauses
- Productivity Gains
- Unit Cost/Shoring
- CR/Enhancements
- Tech Tooling Automation
- Termination Clauses
- Scope of Services

Each dimension provides extracted data, benchmarking against industry standards, and recommendations for optimization.