global:
  name: tech-cost-takeout-app
  replicaCount: 1
  podAnnotations:
    ad.datadoghq.com/tech-cost-takeout-app.logs: '[{"source": "python", "service": "tech-cost-takeout-app"}]'
  image:
    repository: bcgprod.jfrog.io/bcg-cpe-x-gpttechcosttakeout-docker-virtual/tech-cost-takeout-app
    tag: latest
    pullPolicy: Always

  serviceAccount:
    create: true
    annotations: {}

  labels:
    applicationType: "Frontend"
    applicationStack: "NextJs"
    costCenter: "12102-25"
    applicationFacing: "Internal"
    technicalOwner: "Li.Chengcheng"
    environment: "integration"
    tribeName: "SharedApps"
    squadName: "SharedApps"
    sonarqube_project_name: "None"
    veracode_profile_name: "None"

  port: 3000
  livenessProbe:
    httpGet:
      path: /
      port: 3000
      scheme: HTTP
    initialDelaySeconds: 15
    periodSeconds: 10
    failureThreshold: 3
    successThreshold: 1
    timeoutSeconds: 10
  readinessProbe:
    httpGet:
      path: /
      port: 3000
      scheme: HTTP
    initialDelaySeconds: 15
    periodSeconds: 30
    failureThreshold: 3
    successThreshold: 1
    timeoutSeconds: 10

  imagePullSecrets:
    - name: jfrog-docker-registry

  nameOverride: ""
  fullnameOverride: ""

  service:
    type: NodePort
    port: 80
    annotations: {}
    loadBalancerSourceRanges: []
    enableHttp: true

  dnsConfig: {}

  secrets:
    create: false

  configmap: {}

  ingress:
    enabled: false
    hosts:
    annotations:
      nginx.ingress.kubernetes.io/rewrite-target: /$1
      kubernetes.io/ingress.class: nginx

  nodeSelector:
    kubernetes.io/os: linux

  tolerations: []
  affinity: {}

  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0

  podDisruptionBudget:
    minAvailable: 1

  networkPolicy:
    enabled: true
    ingress:
      ipBlock:
        - cidr: 0.0.0.0/0

  autoscaling:
    enabled: false
    minReplicas: 1
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 70

  resources:
    limits:
      cpu: 1
      memory: 256Mi
    requests:
      cpu: 1
      memory: 256Mi
