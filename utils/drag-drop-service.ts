import {
  DragItem,
  DropTarget,
  Provider,
  UnmappedD<PERSON>ument,
  DropResult,
  isValidDrop,
  getDropScenario,
  DropScenario,
  DragDropType,
} from './drag-drop-types';

import {
  transformUnmappedToContractAttachment,
  transformAttachmentBetweenContracts,
  transformAttachmentToUnmapped,
  transformAttachmentToContract,
  transformUnmappedToContract,
  transformContractToAttachment,
  transformContractToUnmapped,
  transformContractBetweenProviders,
  findAttachment,
  findContract,
} from './drag-drop-transformations';

export class DragDropService {
  private static isAttachmentLocked(
    providers: Provider[],
    attachmentId: string,
    contractId: string
  ): { isLocked: boolean; isReplaced: boolean } {
    const attachment = findAttachment(providers, attachmentId, contractId);
    if (!attachment) {
      return { isLocked: false, isReplaced: false };
    }

    return {
      isLocked: attachment.is_existing && !attachment.is_replaced,
      isReplaced: attachment.is_replaced,
    };
  }

  private static isContractLocked(
    providers: Provider[],
    contractId: string
  ): { isLocked: boolean; isReplaced: boolean } {
    const contractData = findContract(providers, contractId);
    if (!contractData) {
      return { isLocked: false, isReplaced: false };
    }

    return {
      isLocked: contractData.contract.is_existing && !contractData.contract.is_replaced,
      isReplaced: contractData.contract.is_replaced,
    };
  }

  static validateDrop(
    source: DragItem,
    target: DropTarget,
    providers: Provider[]
  ): { isValid: boolean; reason?: string } {
    if (!isValidDrop(source, target)) {
      return { isValid: false, reason: "Invalid drop combination" };
    }

    if (source.type === DragDropType.Attachment && source.contractId) {
      const { isLocked, isReplaced } = this.isAttachmentLocked(
        providers,
        source.id,
        source.contractId
      );

      if (isLocked || isReplaced) {
        return {
          isValid: false,
          reason: isLocked ? "Attachment is locked" : "Attachment is replaced"
        };
      }
    }

    if (source.type === DragDropType.Contract) {
      const { isLocked, isReplaced } = this.isContractLocked(
        providers,
        source.id
      );

      if (isLocked || isReplaced) {
        return {
          isValid: false,
          reason: isLocked ? "Contract is locked" : "Contract is replaced"
        };
      }
    }

    return { isValid: true };
  }

  static executeDrop(
    source: DragItem,
    target: DropTarget,
    providers: Provider[],
    unmappedDocuments: UnmappedDocument[]
  ): DropResult {
    const validation = this.validateDrop(source, target, providers);
    if (!validation.isValid) {
      return { success: false, error: validation.reason };
    }

    const scenario = getDropScenario(source, target);
    if (!scenario) {
      return { success: false, error: "Unknown drop scenario" };
    }

    return this.executeTransformation(scenario, source, target, providers, unmappedDocuments);
  }

  private static executeTransformation(
    scenario: DropScenario,
    source: DragItem,
    target: DropTarget,
    providers: Provider[],
    unmappedDocuments: UnmappedDocument[]
  ): DropResult {
    try {
      switch (scenario) {
        case "unmapped-to-contract":
          return transformUnmappedToContractAttachment(providers, unmappedDocuments, source, target);

        case "attachment-to-contract":
          return transformAttachmentBetweenContracts(providers, unmappedDocuments, source, target);

        case "attachment-to-unmapped":
          return transformAttachmentToUnmapped(providers, unmappedDocuments, source, target);

        case "attachment-to-provider":
          return transformAttachmentToContract(providers, unmappedDocuments, source, target);

        case "unmapped-to-provider":
          return transformUnmappedToContract(providers, unmappedDocuments, source, target);

        case "contract-to-contract":
          return transformContractToAttachment(providers, unmappedDocuments, source, target);

        case "contract-to-unmapped":
          return transformContractToUnmapped(providers, unmappedDocuments, source, target);

        case "contract-to-provider":
          return transformContractBetweenProviders(providers, unmappedDocuments, source, target);

        default:
          return { success: false, error: `Unhandled scenario: ${scenario}` };
      }
    } catch (error) {
      console.error("Error executing transformation:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred"
      };
    }
  }

  static getDropPreview(
    source: DragItem,
    target: DropTarget,
    providers: Provider[]
  ): {
    canDrop: boolean;
    message: string;
    scenario?: DropScenario;
  } {
    const validation = this.validateDrop(source, target, providers);
    if (!validation.isValid) {
      return {
        canDrop: false,
        message: validation.reason || "Invalid drop",
      };
    }

    const scenario = getDropScenario(source, target);
    if (!scenario) {
      return {
        canDrop: false,
        message: "Unknown drop scenario",
      };
    }

    const messages: Record<DropScenario, string> = {
      "unmapped-to-contract": "Drop here to add as attachment",
      "attachment-to-contract": "Drop here to move attachment",
      "attachment-to-unmapped": "Drop here to convert to unmapped document",
      "attachment-to-provider": "Drop here to convert attachment to contract",
      "unmapped-to-provider": "Drop here to convert document to contract",
      "contract-to-provider": "Drop here to move contract to this provider",
      "contract-to-contract": "Drop here to convert contract to attachment",
      "contract-to-unmapped": "Drop here to convert contract to unmapped document",
    };

    return {
      canDrop: true,
      message: messages[scenario],
      scenario,
    };
  }
}

export const useDragDropService = () => {
  const executeDrop = (
    source: DragItem,
    target: DropTarget,
    providers: Provider[],
    unmappedDocuments: UnmappedDocument[]
  ) => {
    return DragDropService.executeDrop(source, target, providers, unmappedDocuments);
  };

  const validateDrop = (
    source: DragItem,
    target: DropTarget,
    providers: Provider[]
  ) => {
    return DragDropService.validateDrop(source, target, providers);
  };

  const getDropPreview = (
    source: DragItem,
    target: DropTarget,
    providers: Provider[]
  ) => {
    return DragDropService.getDropPreview(source, target, providers);
  };

  return {
    executeDrop,
    validateDrop,
    getDropPreview,
  };
};
