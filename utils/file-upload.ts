import { toast } from "react-toastify";
import { ALLOWED_FILE_EXTENSIONS } from "@/src/config/documentExtensions";

export const getFilesFromDataTransfer = async (dataTransfer: DataTransfer): Promise<File[]> => {
  const items = Array.from(dataTransfer.items)
    .filter(item => item.kind === 'file');
  const entries = items
    .map(item => (item as any).webkitGetAsEntry())
    .filter(Boolean) as FileSystemEntry[];
  return traverseFileTree(entries);
};

export const traverseFileTree = async (
  entries: FileSystemEntry[],
  path = ''
): Promise<File[]> => {
  const files: File[] = [];
  for (const entry of entries) {
    if (entry.isFile) {
      const file: File = await new Promise((resolve, reject) => {
        (entry as FileSystemFileEntry).file(resolve, reject);
      });
      Object.defineProperty(file, 'webkitRelativePath', {
        value: path + file.name,
        writable: false,
      });
      files.push(file);
    } else if (entry.isDirectory) {
      const dirReader = (entry as FileSystemDirectoryEntry).createReader();
      const nestedEntries: FileSystemEntry[] = await new Promise((resolve, reject) => {
        dirReader.readEntries(resolve, reject);
      });
      const nestedFiles = await traverseFileTree(nestedEntries, path + entry.name + '/');
      files.push(...nestedFiles);
    }
  }
  return files;
};

export const getParentDir = (file: File) => {
  const path = file.webkitRelativePath || file.name;
  const idx = path.lastIndexOf('/');
  return idx > -1 ? path.slice(0, idx + 1) : '/';
};

export interface FolderStructure {
  name: string;
  path: string;
  files: File[];
  folders: FolderStructure[];
  isOpen?: boolean;
}

export const organizeFilesByFolder = (files: File[]): FolderStructure => {
  const root: FolderStructure = {
    name: 'root',
    path: '/',
    files: [],
    folders: [],
    isOpen: true
  };

  files.forEach(file => {
    const path = file.webkitRelativePath || file.name;
    const pathParts = path.split('/').filter(Boolean);

    if (pathParts.length <= 1) {
      root.files.push(file);
      return;
    }

    let currentFolder = root;
    pathParts.pop();

    for (const folderName of pathParts) {
      let folder = currentFolder.folders.find(f => f.name === folderName);

      if (!folder) {
        folder = {
          name: folderName,
          path: currentFolder.path === '/'
            ? `/${folderName}/`
            : `${currentFolder.path}${folderName}/`,
          files: [],
          folders: [],
          isOpen: true
        };
        currentFolder.folders.push(folder);
      }

      currentFolder = folder;
    }

    currentFolder.files.push(file);
  });

  return root;
};

export const simulateFileUpload = (
  fileName: string,
  setUploadProgress: React.Dispatch<React.SetStateAction<Record<string, number>>>,
  setUploadStatus: React.Dispatch<React.SetStateAction<Record<string, "pending" | "success" | "error">>>
) => {
  let progress = 0;
  const interval = setInterval(() => {
    progress += Math.floor(Math.random() * 10) + 5;
    if (progress >= 100) {
      progress = 100;
      clearInterval(interval);

      setUploadStatus((prev) => ({
        ...prev,
        [fileName]: "success", // Always return success
      }));
    }

    setUploadProgress((prev) => ({
      ...prev,
      [fileName]: progress,
    }));
  }, 300);
};

export const processFiles = (
  fileArray: File[],
  setFiles: React.Dispatch<React.SetStateAction<File[]>>,
  uploadProgress: Record<string, number>,
  setUploadProgress: React.Dispatch<React.SetStateAction<Record<string, number>>>,
  uploadStatus: Record<string, "pending" | "success" | "error">,
  setUploadStatus: React.Dispatch<React.SetStateAction<Record<string, "pending" | "success" | "error">>>
) => {
  setFiles(prev => [...prev, ...fileArray]);

  const newProgress = { ...uploadProgress };
  const newStatus = { ...uploadStatus };
  fileArray.forEach(file => {
    const key = file.name;
    newProgress[key] = 0;
    newStatus[key] = 'pending';
  });
  setUploadProgress(newProgress);
  setUploadStatus(newStatus);

  fileArray.forEach(file => {
    const key = file.name;
    simulateFileUpload(key, setUploadProgress, setUploadStatus);
  });
};

export const filterFiles = (fileArray: File[]): File[] => {
  return fileArray.filter(file => {
    if (file.name.startsWith('.') || file.name.includes('~') || file.name.endsWith('.tmp')) {
      toast.warn(`Ignored temporary or hidden file: ${file.name}`);
      return false;
    }
    const ext = file.name.split('.').pop()?.toLowerCase() ?? '';

    if (!ALLOWED_FILE_EXTENSIONS.join(',').includes(ext)) {
      toast.error(`Invalid file type: ${file.name}. Input Type: ${ext}. Allowed types: ${ALLOWED_FILE_EXTENSIONS.join(', ')}`);
      return false;
    }

    return true;
  });
};

export const handleDragEnter = (
  e: React.DragEvent<HTMLDivElement>,
  setIsDragging: React.Dispatch<React.SetStateAction<boolean>>
) => {
  e.preventDefault();
  e.stopPropagation();
  setIsDragging(true);
};

export const handleDragLeave = (
  e: React.DragEvent<HTMLDivElement>,
  setIsDragging: React.Dispatch<React.SetStateAction<boolean>>
) => {
  e.preventDefault();
  e.stopPropagation();
  setIsDragging(false);
};

export const handleDragOver = (
  e: React.DragEvent<HTMLDivElement>,
  isDragging: boolean,
  setIsDragging: React.Dispatch<React.SetStateAction<boolean>>
) => {
  e.preventDefault();
  e.stopPropagation();
  if (!isDragging) {
    setIsDragging(true);
  }
};

export const handleFileDrop = async (
  e: React.DragEvent<HTMLDivElement>,
  setIsDragging: React.Dispatch<React.SetStateAction<boolean>>,
  processFilesFn: (files: File[]) => void,
  filterFilesFn: (files: File[]) => File[] = filterFiles
) => {
  e.preventDefault();
  e.stopPropagation();
  setIsDragging(false);

  let fileArray: File[] = [];
  if (e.dataTransfer.items) {
    fileArray = await getFilesFromDataTransfer(e.dataTransfer);
  } else if (e.dataTransfer.files) {
    fileArray = Array.from(e.dataTransfer.files);
  }

  const filtered = filterFilesFn(fileArray);
  processFilesFn(filtered);
};

export const handleFileInputChange = (
  e: React.ChangeEvent<HTMLInputElement>,
  processFilesFn: (files: File[]) => void,
  filterFilesFn: (files: File[]) => File[] = filterFiles
) => {
  if (!e.target.files) return;
  const fileArray = Array.from(e.target.files);
  const filtered = filterFilesFn(fileArray);
  processFilesFn(filtered);
};
