export enum DragDropType {
  Attachment = "attachment",
  Unmapped = "unmapped",
  Contract = "contract",
  Provider = "provider"
}

export interface DragItem {
  id: string;
  type: DragDropType;
  contractId?: string;
}

export interface DropTarget {
  id: string;
  type: DragDropType;
}

export interface DragState {
  isDragging: string | null;
  dragOverTarget: string | null;
  isAnyDragging: boolean;
  dragItem: DragItem | null;
  dragNode: HTMLElement | null;
}

export interface Provider {
  id: string;
  name: string;
  contracts: Contract[];
}

export interface Contract {
  id: string;
  name: string;
  type: string;
  contractType: string;
  startDate: string;
  endDate: string;
  contractValue: string;
  parties: string[];
  confidence: number;
  status: string;
  is_replaced: boolean;
  is_existing: boolean;
  attachments: Attachment[];
  moved?: boolean;
}

export interface Attachment {
  id: string;
  name: string;
  type: string;
  confidence: number;
  moved?: boolean;
  is_existing: boolean;
  is_replaced: boolean;
}

export interface UnmappedDocument {
  id: string;
  name: string;
  type: string;
  status: string;
  confidence: number;
  moved?: boolean;
  is_existing: boolean;
}

export interface DropResult {
  success: boolean;
  updatedProviders?: Provider[];
  updatedUnmappedDocuments?: UnmappedDocument[];
  error?: string;
}

export type DropScenario =
  | "unmapped-to-contract"
  | "attachment-to-contract"
  | "attachment-to-unmapped"
  | "attachment-to-provider"
  | "unmapped-to-provider"
  | "contract-to-provider"
  | "contract-to-contract"
  | "contract-to-unmapped";

export interface DropOperation {
  scenario: DropScenario;
  source: DragItem;
  target: DropTarget;
  sourceData?: any;
  targetData?: any;
}

export const DROP_VALIDATION_RULES: Record<string, boolean> = {
  [`${DragDropType.Unmapped}-${DragDropType.Contract}`]: true,
  [`${DragDropType.Attachment}-${DragDropType.Contract}`]: true,
  [`${DragDropType.Attachment}-${DragDropType.Unmapped}`]: true,
  [`${DragDropType.Attachment}-${DragDropType.Provider}`]: true,
  [`${DragDropType.Unmapped}-${DragDropType.Provider}`]: true,
  [`${DragDropType.Contract}-${DragDropType.Contract}`]: true,
  [`${DragDropType.Contract}-${DragDropType.Unmapped}`]: true,
  [`${DragDropType.Contract}-${DragDropType.Provider}`]: true,
};

export const isValidDrop = (source: DragItem, target: DropTarget): boolean => {
  if (source.id === target.id) return false;

  if (source.type === DragDropType.Attachment && target.type === DragDropType.Contract && source.contractId === target.id) {
    return false;
  }

  const key = `${source.type}-${target.type}`;
  return DROP_VALIDATION_RULES[key] || false;
};


export const getDropScenario = (source: DragItem, target: DropTarget): DropScenario | null => {
  const scenarioMap: Record<string, DropScenario> = {
    [`${DragDropType.Unmapped}-${DragDropType.Contract}`]: "unmapped-to-contract",
    [`${DragDropType.Attachment}-${DragDropType.Contract}`]: "attachment-to-contract",
    [`${DragDropType.Attachment}-${DragDropType.Unmapped}`]: "attachment-to-unmapped",
    [`${DragDropType.Attachment}-${DragDropType.Provider}`]: "attachment-to-provider",
    [`${DragDropType.Unmapped}-${DragDropType.Provider}`]: "unmapped-to-provider",
    [`${DragDropType.Contract}-${DragDropType.Provider}`]: "contract-to-provider",
    [`${DragDropType.Contract}-${DragDropType.Contract}`]: "contract-to-contract",
    [`${DragDropType.Contract}-${DragDropType.Unmapped}`]: "contract-to-unmapped",
  };

  const key = `${source.type}-${target.type}`;
  return scenarioMap[key] || null;
};

export const getDropMessage = (source: DragItem, target: DropTarget): string => {
  const scenario = getDropScenario(source, target);

  const messages: Record<DropScenario, string> = {
    "unmapped-to-contract": "Drop here to add as attachment",
    "attachment-to-contract": "Drop here to move attachment",
    "attachment-to-unmapped": "Drop here to convert to unmapped document",
    "attachment-to-provider": "Drop here to convert attachment to contract",
    "unmapped-to-provider": "Drop here to convert document to contract",
    "contract-to-provider": "Drop here to move contract to this provider",
    "contract-to-contract": "Drop here to convert contract to attachment",
    "contract-to-unmapped": "Drop here to convert contract to unmapped document",
  };

  return scenario ? messages[scenario] : "Invalid drop target";
};
