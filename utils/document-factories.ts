export interface ContractData {
  id?: string;
  name: string;
  type?: string;
  contractType?: string;
  startDate?: string;
  endDate?: string;
  contractValue?: string;
  parties?: string[];
  confidence?: number;
  status?: string;
  is_replaced?: boolean;
  is_existing?: boolean;
  attachments?: any[];
  moved?: boolean;
}

export interface AttachmentData {
  id?: string;
  name: string;
  type?: string;
  confidence?: number;
  moved?: boolean;
  is_existing?: boolean;
  is_replaced?: boolean;
}

export interface UnmappedDocumentData {
  id?: string;
  name: string;
  type?: string;
  status?: string;
  confidence?: number;
  moved?: boolean;
  is_existing?: boolean;
}

export const createContract = (data: ContractData): ContractData => {
  const now = new Date();
  const oneYearFromNow = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);

  return {
    id: data.id || `contract-${Date.now()}`,
    name: data.name,
    type: "Contract",
    contractType: data.contractType || "Other",
    startDate: data.startDate || now.toISOString().split('T')[0],
    endDate: data.endDate || oneYearFromNow.toISOString().split('T')[0],
    contractValue: data.contractValue || "",
    parties: data.parties || [],
    confidence: data.confidence ?? 0,
    status: data.status || "processed",
    is_replaced: data.is_replaced || false,
    is_existing: data.is_existing || false,
    attachments: data.attachments || [],
    moved: true,
  };
};

export const createAttachment = (data: AttachmentData): AttachmentData => {
  return {
    id: data.id || `attachment-${Date.now()}`,
    name: data.name,
    type: "Attachment",
    confidence: data.confidence ?? 0,
    moved: true,
    is_existing: data.is_existing || false,
    is_replaced: data.is_replaced || false,
  };
};

export const createUnmappedDocument = (data: UnmappedDocumentData): UnmappedDocumentData => {
  return {
    id: data.id || `unmapped-${Date.now()}`,
    name: data.name,
    type: "Unknown",
    status: data.status || "processed",
    confidence: data.confidence ?? 0,
    moved: true,
    is_existing: data.is_existing || false,
  };
};

export const contractToAttachment = (contract: ContractData): AttachmentData => {
  return createAttachment({
    name: contract.name,
    type: "Contract",
    confidence: 0,
    moved: true,
    is_existing: false,
    is_replaced: false,
  });
};

export const attachmentToContract = (attachment: AttachmentData): ContractData => {
  return createContract({
    name: attachment.name,
    type: "Contract",
    contractType: "Other",
    confidence: 0,
    moved: true,
    is_existing: false,
  });
};

export const contractToUnmappedDocument = (contract: ContractData): UnmappedDocumentData => {
  return createUnmappedDocument({
    name: contract.name,
    type: "Contract",
    confidence: 0,
    moved: true,
    is_existing: false,
  });
};

export const attachmentToUnmappedDocument = (attachment: AttachmentData): UnmappedDocumentData => {
  return createUnmappedDocument({
    name: attachment.name,
    type: "Attachment",
    confidence: attachment.confidence || 0,
    moved: true,
    is_existing: false,
  });
};

export const unmappedDocumentToAttachment = (doc: UnmappedDocumentData): AttachmentData => {
  return createAttachment({
    id: `moved-${doc.id}`,
    name: doc.name,
    type: doc.type || "Attachment",
    confidence: doc.confidence || 0,
    moved: true,
    is_existing: false,
    is_replaced: false,
  });
};

export const unmappedDocumentToContract = (doc: UnmappedDocumentData): ContractData => {
  return createContract({
    name: doc.name,
    type: "Contract",
    contractType: "Other",
    confidence: 0,
    moved: true,
    is_existing: false,
  });
};

export const attachmentsToUnmappedDocuments = (attachments: AttachmentData[]): UnmappedDocumentData[] => {
  return attachments.map((attachment, index) =>
    createUnmappedDocument({
      id: `unmapped-attachment-${Date.now()}-${index}`,
      name: attachment.name,
      type: "Attachment",
      confidence: attachment.confidence || 0,
      moved: true,
      is_existing: false,
    })
  );
};
