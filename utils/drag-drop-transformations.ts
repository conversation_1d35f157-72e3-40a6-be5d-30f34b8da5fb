import {
  Provider,
  Contract,
  Attachment,
  UnmappedDocument,
  DragItem,
  DropTarget,
  DropResult,
} from './drag-drop-types';

import {
  createAttachment,
  createContract,
  createUnmappedDocument,
  contractToAttachment,
  attachmentToContract,
  contractToUnmappedDocument,
  attachmentToUnmappedDocument,
  unmappedDocumentToAttachment,
  unmappedDocumentToContract,
  attachmentsToUnmappedDocuments,
} from './document-factories';

export const findContract = (providers: Provider[], contractId: string): { contract: Contract; providerId: string } | null => {
  for (const provider of providers) {
    const contract = provider.contracts.find(c => c.id === contractId);
    if (contract) {
      return { contract, providerId: provider.id };
    }
  }
  return null;
};

export const findAttachment = (providers: Provider[], attachmentId: string, contractId: string): Attachment | null => {
  const contractData = findContract(providers, contractId);
  if (!contractData) return null;

  return contractData.contract.attachments.find(att => att.id === attachmentId) || null;
};

export const findUnmappedDocument = (unmappedDocuments: UnmappedDocument[], docId: string): UnmappedDocument | null => {
  return unmappedDocuments.find(doc => doc.id === docId) || null;
};

export const removeContractFromProviders = (providers: Provider[], contractId: string): Provider[] => {
  return providers.map(provider => ({
    ...provider,
    contracts: provider.contracts.filter(contract => contract.id !== contractId),
  }));
};

export const removeAttachmentFromContract = (providers: Provider[], attachmentId: string, contractId: string): Provider[] => {
  return providers.map(provider => ({
    ...provider,
    contracts: provider.contracts.map(contract => {
      if (contract.id === contractId) {
        return {
          ...contract,
          attachments: contract.attachments.filter(att => att.id !== attachmentId),
        };
      }
      return contract;
    }),
  }));
};

export const addAttachmentToContract = (providers: Provider[], attachment: Attachment, contractId: string): Provider[] => {
  return providers.map(provider => ({
    ...provider,
    contracts: provider.contracts.map(contract => {
      if (contract.id === contractId) {
        return {
          ...contract,
          attachments: [...contract.attachments, attachment],
        };
      }
      return contract;
    }),
  }));
};


export const addContractToProvider = (providers: Provider[], contract: Contract, providerId: string): Provider[] => {
  return providers.map(provider => {
    if (provider.id === providerId) {
      return {
        ...provider,
        contracts: [...provider.contracts, contract],
      };
    }
    return provider;
  });
};


export const moveContractBetweenProviders = (
  providers: Provider[],
  contractId: string,
  targetProviderId: string
): Provider[] => {
  const contractData = findContract(providers, contractId);
  if (!contractData || contractData.providerId === targetProviderId) {
    return providers;
  }

  const withoutContract = removeContractFromProviders(providers, contractId);
  return addContractToProvider(withoutContract, contractData.contract, targetProviderId);
};


export const transformUnmappedToContractAttachment = (
  providers: Provider[],
  unmappedDocuments: UnmappedDocument[],
  source: DragItem,
  target: DropTarget
): DropResult => {
  const docToMove = findUnmappedDocument(unmappedDocuments, source.id);
  if (!docToMove) {
    return { success: false, error: "Document not found" };
  }

  const attachment = unmappedDocumentToAttachment(docToMove);
  const updatedProviders = addAttachmentToContract(providers, attachment, target.id);
  const updatedUnmappedDocuments = unmappedDocuments.filter(doc => doc.id !== source.id);

  return {
    success: true,
    updatedProviders,
    updatedUnmappedDocuments,
  };
};


export const transformAttachmentBetweenContracts = (
  providers: Provider[],
  unmappedDocuments: UnmappedDocument[],
  source: DragItem,
  target: DropTarget
): DropResult => {
  if (!source.contractId) {
    return { success: false, error: "Source contract ID missing" };
  }

  const attachmentToMove = findAttachment(providers, source.id, source.contractId);
  if (!attachmentToMove) {
    return { success: false, error: "Attachment not found" };
  }

  const withoutAttachment = removeAttachmentFromContract(providers, source.id, source.contractId);
  const movedAttachment = { ...attachmentToMove, moved: true };
  const updatedProviders = addAttachmentToContract(withoutAttachment, movedAttachment, target.id);

  return {
    success: true,
    updatedProviders,
    updatedUnmappedDocuments: unmappedDocuments,
  };
};


export const transformAttachmentToUnmapped = (
  providers: Provider[],
  unmappedDocuments: UnmappedDocument[],
  source: DragItem,
  target: DropTarget
): DropResult => {
  if (!source.contractId) {
    return { success: false, error: "Source contract ID missing" };
  }

  const attachmentToMove = findAttachment(providers, source.id, source.contractId);
  if (!attachmentToMove) {
    return { success: false, error: "Attachment not found" };
  }

  const updatedProviders = removeAttachmentFromContract(providers, source.id, source.contractId);
  const newUnmappedDoc = attachmentToUnmappedDocument(attachmentToMove);
  const updatedUnmappedDocuments = [...unmappedDocuments, newUnmappedDoc];

  return {
    success: true,
    updatedProviders,
    updatedUnmappedDocuments,
  };
};


export const transformAttachmentToContract = (
  providers: Provider[],
  unmappedDocuments: UnmappedDocument[],
  source: DragItem,
  target: DropTarget
): DropResult => {
  if (!source.contractId) {
    return { success: false, error: "Source contract ID missing" };
  }

  const attachmentToMove = findAttachment(providers, source.id, source.contractId);
  if (!attachmentToMove) {
    return { success: false, error: "Attachment not found" };
  }

  const updatedProviders = removeAttachmentFromContract(providers, source.id, source.contractId);
  const newContract = attachmentToContract(attachmentToMove);
  const finalProviders = addContractToProvider(updatedProviders, newContract, target.id);

  return {
    success: true,
    updatedProviders: finalProviders,
    updatedUnmappedDocuments: unmappedDocuments,
  };
};


export const transformUnmappedToContract = (
  providers: Provider[],
  unmappedDocuments: UnmappedDocument[],
  source: DragItem,
  target: DropTarget
): DropResult => {
  const docToMove = findUnmappedDocument(unmappedDocuments, source.id);
  if (!docToMove) {
    return { success: false, error: "Document not found" };
  }

  const newContract = unmappedDocumentToContract(docToMove);
  const updatedProviders = addContractToProvider(providers, newContract, target.id);
  const updatedUnmappedDocuments = unmappedDocuments.filter(doc => doc.id !== source.id);

  return {
    success: true,
    updatedProviders,
    updatedUnmappedDocuments,
  };
};

export const transformContractToAttachment = (
  providers: Provider[],
  unmappedDocuments: UnmappedDocument[],
  source: DragItem,
  target: DropTarget
): DropResult => {
  const contractData = findContract(providers, source.id);
  if (!contractData) {
    return { success: false, error: "Contract not found" };
  }

  const updatedProviders = removeContractFromProviders(providers, source.id);
  const newAttachment = contractToAttachment(contractData.contract);
  const finalProviders = addAttachmentToContract(updatedProviders, newAttachment, target.id);

  const attachmentsAsUnmapped = attachmentsToUnmappedDocuments(contractData.contract.attachments || []);
  const updatedUnmappedDocuments = [...unmappedDocuments, ...attachmentsAsUnmapped];

  return {
    success: true,
    updatedProviders: finalProviders,
    updatedUnmappedDocuments,
  };
};

export const transformContractToUnmapped = (
  providers: Provider[],
  unmappedDocuments: UnmappedDocument[],
  source: DragItem,
  target: DropTarget
): DropResult => {
  const contractData = findContract(providers, source.id);
  if (!contractData) {
    return { success: false, error: "Contract not found" };
  }

  const updatedProviders = removeContractFromProviders(providers, source.id);

  const newUnmappedDoc = contractToUnmappedDocument(contractData.contract);

  const attachmentsAsUnmapped = attachmentsToUnmappedDocuments(contractData.contract.attachments || []);
  const updatedUnmappedDocuments = [
    ...unmappedDocuments,
    newUnmappedDoc,
    ...attachmentsAsUnmapped,
  ];

  return {
    success: true,
    updatedProviders,
    updatedUnmappedDocuments,
  };
};

export const transformContractBetweenProviders = (
  providers: Provider[],
  unmappedDocuments: UnmappedDocument[],
  source: DragItem,
  target: DropTarget
): DropResult => {
  const contractData = findContract(providers, source.id);
  if (!contractData) {
    return { success: false, error: "Contract not found" };
  }

  if (contractData.providerId === target.id) {
    return { success: false, error: "Contract is already in this provider" };
  }

  const updatedProviders = moveContractBetweenProviders(providers, source.id, target.id);

  return {
    success: true,
    updatedProviders,
    updatedUnmappedDocuments: unmappedDocuments,
  };
};
