import { create } from "zustand";
import { devtools } from "zustand/middleware";

import { components } from "../api/api-types";

type FileDeduplicationResponseDTO = components["schemas"]["FileDeduplicationResponseDTO"];

interface StoredFile {
    file: File;
    relativePath: string;
    replacedFile?: string; // Name of the file this is replacing (if any)
}

interface ProjectFileState {
    files: StoredFile[];
    deduplicationData: FileDeduplicationResponseDTO | null;
    deduplicatedFileNames: string[];
    projectId: string | null;

    setFiles: (files: StoredFile[]) => void;
    setDeduplicationData: (data: FileDeduplicationResponseDTO) => void;
    setDeduplicatedFileNames: (fileNames: string[]) => void;
    setProjectId: (id: string) => void;
    clearAll: () => void;
}

export const useProjectFileStore = create<ProjectFileState>()(
    devtools(
        (set) => ({
            files: [],
            deduplicationData: null,
            deduplicatedFileNames: [],
            projectId: null,

            setFiles: (files) => set({ files }, false, "setFiles"),
            setDeduplicationData: (data) => set({ deduplicationData: data }, false, "setDeduplicationData"),
            setDeduplicatedFileNames: (fileNames) => set({ deduplicatedFileNames: fileNames }, false, "setDeduplicatedFileNames"),
            setProjectId: (id) => set({ projectId: id }, false, "setProjectId"),
            clearAll: () =>
                set(
                    {
                        files: [],
                        deduplicationData: null,
                        deduplicatedFileNames: [],
                        projectId: null,
                    },
                    false,
                    "clearAll"
                ),
        }),
        { name: "ProjectFileStore" }
    )
);