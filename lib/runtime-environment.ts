export function getApiUrl(): string {
    if (typeof window !== 'undefined') {
        const hostname = window.location.hostname;

        if (hostname.includes('integration')) {
            return 'https://api-euc-internal.integration.smp.bcg.com/tech-cost-takeout-api/api/';
        } else if (hostname.includes('preproduction')) {
            return 'https://api-euc-internal.preproduction.smp.bcg.com/tech-cost-takeout-api/api/';
        } else if (hostname.includes('production')) {
            return 'https://api-euc.internal.production.smp.bcg.com/tech-cost-takeout-api/api/';
        } else {
            console.warn('Unknown environment, defaulting API URL to localhost');
            return 'http://localhost:8000/api/';
        }
    }

    console.warn('Not running in browser, returning empty API URL');
    return '';
}

export function getApiKey(): string {
    if (typeof window !== 'undefined') {
        const hostname = window.location.hostname;

        if (hostname.includes('integration')) {
            return 'AskQliXde7Y5q2lPbMvZSmZS8WNtxEej';
        } else if (hostname.includes('preproduction')) {
            return '8MTQWRKLmiIiC035xF5a3nNIuKDK2BLs';
        } else if (hostname.includes('production')) {
            return 'production-key';
        } else {
            console.warn('Unknown environment, defaulting API key to empty');
            return '';
        }
    }

    console.warn('Not running in browser, returning empty API key');
    return '';
}