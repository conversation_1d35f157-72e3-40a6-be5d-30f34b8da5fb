@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Base colors - keeping our original colors */
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;
    --card: 0 0% 98%;
    --card-foreground: 0 0% 0%;
    --component-background: 0 0% 100%;
    --component-foreground: 0 0% 0%;
    --user-message-background: 135 62% 80%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;
    --primary: 153 66% 29%;
    --primary-foreground: 0 0% 100%;
    --secondary: 30 20% 96%;
    --secondary-foreground: 0 0% 0%;
    --muted: 0 0% 98%;
    --muted-foreground: 0 0% 40%;
    --accent: 117 86% 70%;
    --accent-foreground: 0 0% 0%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --info: 217 92% 45%;
    --info-foreground: 0 0% 100%;
    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 117 86% 70%;
    --radius: 0.75rem;
    --sidebar-background: 30 20% 96%;
    --sidebar-foreground: 0 0% 0%;
    --sidebar-border: 0 0% 90%;
    --sidebar-muted: 0 0% 96%;
  }

  body {
    @apply bg-gradient-to-br from-white to-gray-50;
  }
}

/* Enhanced glassmorphism effect */
.glassmorphism {
  @apply bg-white/70 backdrop-blur-md;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
}

/* Card styles */
.card-glass {
  @apply bg-white/80 backdrop-blur-sm border border-white/20 shadow-sm;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03), inset 0 0 0 1px
    rgba(255, 255, 255, 0.15);
}

/* Enhanced neumorphism effects */
.neumorphism {
  @apply bg-white/90;
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.03), -4px -4px 8px rgba(255, 255, 255, 0.5);
}

.neumorphism-inset {
  @apply bg-white/80;
  box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.03), inset -2px -2px 4px rgba(255, 255, 255, 0.5);
}

/* Navigation styles */
.nav-item {
  @apply flex items-center gap-3 px-4 py-2 rounded-lg text-gray-600 hover:bg-white/50 hover:text-gray-900 transition-all;
}

.nav-item.active {
  @apply bg-white/80 text-gray-900 shadow-sm;
}

/* Table styles */
.table-glass {
  @apply bg-white/70 backdrop-blur-sm rounded-lg border border-white/20;
}

.table-glass th {
  @apply bg-white/50 text-gray-600 font-medium px-4 py-3;
}

.table-glass td {
  @apply px-4 py-3 border-t border-gray-100/20;
}
