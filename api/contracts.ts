import { useMutation, UseMutationResult, useQueryClient } from "@tanstack/react-query";
import { api } from "./client";
import { components } from "./api-types";
import { useQuery } from "@tanstack/react-query";

type ContractDTO = components["schemas"]["ContractDTO"];



export const useContract = (
    contractId: string,
) => {
    return useQuery<ContractDTO>({
        queryKey: ["projects", contractId],
        queryFn: async () => {
            const project = await api<ContractDTO>(`contracts/${contractId}`, {
                method: "GET",
            });
            return project;
        }
    });
};
