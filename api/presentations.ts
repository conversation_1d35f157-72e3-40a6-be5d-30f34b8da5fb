import { useMutation, useQuery } from "@tanstack/react-query";
import { api } from "./client";
import { components } from "./api-types"
import { saveAs } from "file-saver";

type PresentationResponse = components["schemas"]["PresentationResponse"]
type PresentationTaskStatus = components["schemas"]["PresentationTaskStatus"]
type ExcelResponse = components["schemas"]["ExcelResponse"]
type ExcelTaskStatus = components["schemas"]["ExcelTaskStatus"]

export function useRequestPresentation() {
  return useMutation<PresentationResponse, Error, { projectId: string; contractId: string }>({
    mutationFn: async ({ projectId, contractId }) => {
      return api<PresentationResponse>(`presentations/project/${projectId}/${contractId}`, {
        method: "POST",
      });
    },
  });
}

export function useRequestContractExcel() {
  return useMutation<ExcelResponse, Error, { projectId: string; contractId: string }>({
    mutationFn: async ({ projectId, contractId }) => {
      // Send POST to Excel export endpoint for the given contract
      return api<ExcelResponse>(`excel/project/${projectId}/${contractId}`, {
        method: "POST",
      });
    },
  });
}

export function useRequestProjectExcel() {
  return useMutation<ExcelResponse, Error, { projectId: string;}>({
    mutationFn: async ({ projectId}) => {
      // Send POST to Excel export endpoint for the given project
      return api<ExcelResponse>(`excel/project/${projectId}`, {
        method: "POST",
      });
    },
  });
}

/**
 * Hook to check the status of a presentation task
 */
export function usePresentationStatus(taskId: string | null, options?: { enabled?: boolean; refetchInterval?: number | false }) {
  return useQuery<PresentationTaskStatus, Error>({
    queryKey: ["presentation-status", taskId],
    queryFn: async () => {
      return api<PresentationTaskStatus>(`presentations/status/${taskId}`, {
        method: "GET",
      });
    },
    enabled: !!taskId && (options?.enabled !== false),
    refetchInterval: options?.refetchInterval,
  });
}

/**
 * Hook to check the status of an Excel export task
 */
export function useExcelStatus(
  taskId: string | null,
  options?: { enabled?: boolean; refetchInterval?: number | false }
) {
  return useQuery<ExcelTaskStatus, Error>({
    queryKey: ["excel-status", taskId],
    queryFn: async () => {
      return api<ExcelTaskStatus>(`excel/status/${taskId}`, {
        method: "GET",
      });
    },
    enabled: !!taskId && options?.enabled !== false,
    refetchInterval: options?.refetchInterval,
  });
}

/**
 * Function to download a presentation using the existing api client
 */
export async function downloadPresentation(taskId: string, filePath?: string): Promise<void> {
  try {
    const response = await api<Response>(`presentations/download/${taskId}`, {
      method: "GET",
      returnRawResponse: true,
    });

    const blob = await response.blob();

    // Determine default filename from filePath parameter or fallback
    let filename = filePath ? filePath.split('/').pop() || "project_presentation.pptx" : "project_presentation.pptx";
    // Override filename if content-disposition header is present
    const contentDisposition = response.headers.get("content-disposition");
    if (contentDisposition) {
      const match = /filename="(.+)"/.exec(contentDisposition);
      if (match && match[1]) {
        filename = match[1];
      }
    }
    // Save the file
    saveAs(blob, filename);
  } catch (error) {
    console.error("Powerpoint download error:", error);
    throw error;
  }
}


/**
 * Function to download an Excel file using the existing api client
 */
export async function downloadExcel(taskId: string, filePath?: string): Promise<void> {
  try {
    const response = await api<Response>(`excel/download/${taskId}`, {
      method: "GET",
      returnRawResponse: true,
    });

    const blob = await response.blob();

    // Determine default filename from filePath parameter or fallback
    let filename = filePath ? filePath.split('/').pop() || "project_export.xlsx" : "project_export.xlsx";
    // Override filename if content-disposition header is present
    const contentDisposition = response.headers.get("content-disposition");
    if (contentDisposition) {
      const match = /filename="(.+)"/.exec(contentDisposition);
      if (match && match[1]) {
        filename = match[1];
      }
    }

    // Save the file
    saveAs(blob, filename);
  } catch (error) {
    console.error("Excel download error:", error);
    throw error;
  }
}