import { useQuery, useMutation } from "@tanstack/react-query"
import { api } from "./client"
import { components } from "./api-types"
import { queryClient } from "./query-client";

export type DocumentDTO = components["schemas"]["DocumentDTO"];
export type IngestionResponse = components["schemas"]["IngestionResponse"];
export type IngestionResponseDTO = components["schemas"]["IngestionResponseDTO"];
export type UploadResponseDTO = components["schemas"]["UploadResponseDTO"];

export function useGetDocuments(projectId: string) {
  return useQuery<DocumentDTO[], Error>({
    queryKey: ["documents", projectId],
    queryFn: async () => {
      return api<DocumentDTO[]>(`document/${projectId}`, {
        method: "GET",
      })
    },
    enabled: Boolean(projectId),
    refetchInterval: (response) => {
      const documents = response.state.data;
      const hasInProgress = documents ? documents?.some(
        (p) => p.status === "PENDING"
      ) : true;
      return hasInProgress ? 2500 : false
    },
  })
}

export function useUploadListing() {
  return useMutation<UploadResponseDTO, Error, {projectId: string | null, files: FormData}>({
    mutationFn: async ({
      files,
      projectId
    }: {
      files: FormData,
      projectId: string | null
    }) => {
      return api<UploadResponseDTO>(`document/${projectId}/upload`, {
        method: "POST",
        body: files,
      });
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["documents"] });
      return data;
    },
  });
}

export function useProcessFile(projectId: string) {
  return useMutation<DocumentDTO, Error, FormData>({
    mutationFn: async (file: FormData) => {
      return api<DocumentDTO>(`document/${projectId}/upload/process`, {
        method: "POST",
        body: file,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["documents", projectId] });
    },
  });
}


export function useTriggerIngestion() {
  return useMutation<IngestionResponse, Error, string>({
    mutationFn: async (projectId: string) => {
      return api<IngestionResponse>(`document/${projectId}/ingest`, {
        method: "POST",
      });
    },
  });
}

export function useTriggerIngestionContractsAndAttachments() {
  return useMutation<IngestionResponseDTO, Error, { projectId: string; contract_attachment_data: any }>({
    mutationFn: async ({ projectId, contract_attachment_data }) => {
      return api<IngestionResponseDTO>(`document/${projectId}/ingest-contract-with-attachments`, {
        method: "POST",
        body: contract_attachment_data,
        headers: {
          "Content-Type": "application/json",
        },
      });
    },
  });
}
