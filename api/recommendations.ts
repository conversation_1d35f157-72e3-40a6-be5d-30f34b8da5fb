import { useQuery } from "@tanstack/react-query";
import { api } from "./client";
import { components } from "./api-types";

type RecommendationResultsDTO = components["schemas"]["RecommendationResultsDTO"];

export function useGetRecommendationData(project_id: string, contract_id: string, transparency_feature_id: string) {
    return useQuery<RecommendationResultsDTO, Error>({
        queryKey: ["recommendation", project_id, transparency_feature_id],
        queryFn: async () => {
            return api<RecommendationResultsDTO>(`recommendations/${project_id}/${contract_id}/${transparency_feature_id}`, {
                method: "GET",
            });
        },
    });
}