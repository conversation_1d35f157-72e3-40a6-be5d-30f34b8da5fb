import { useMutation, UseMutationResult, useQueryClient, useQuery } from "@tanstack/react-query";
import { api } from "./client";
import { components } from "./api-types";
import { queryClient } from "./query-client";
type TransparencyFeaturesResponseDTO = components["schemas"]["TransparencyFeaturesResponseDTO"];
type TransparencyFeatureResultDTO = components["schemas"]["TransparencyFeatureResultDTO"];
type CustomTransparencyFeatureCreate = components["schemas"]["CustomTransparencyFeatureCreate"];
type CustomTransparencyFeatureUpdate = components["schemas"]["CustomTransparencyFeatureUpdate"];
type CustomTransparencyFeatureResponse = components["schemas"]["CustomTransparencyFeatureResponse"];
export function useGetTransparencyFeatures(project_id: string) {
  return useQuery<TransparencyFeaturesResponseDTO, Error>({
    queryKey: ["transparency_features", project_id],
    queryFn: async () => {
      return api<TransparencyFeaturesResponseDTO>(`transparency_features/${project_id}`, {
        method: "GET",
      });
    },
  });
}

export function useGetTransparencyFeatureData(project_id: string, contract_id: string, transparency_feature_id: string) {
  return useQuery<TransparencyFeatureResultDTO, Error>({
    queryKey: ["transparency_feature_data", project_id, contract_id, transparency_feature_id],
    queryFn: async () => {
      return api<TransparencyFeatureResultDTO>(`transparency_features/${project_id}/${contract_id}/${transparency_feature_id}`, {
        method: "GET",
      });
    },
  });
}

export const useCreateCustomTransparencyFeature = (projectId: string): UseMutationResult<CustomTransparencyFeatureResponse, Error, CustomTransparencyFeatureCreate> => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      name,
      question,
      generation_instruction,
    }: CustomTransparencyFeatureCreate) => {
      const response = await api<CustomTransparencyFeatureResponse>(`transparency_features/${projectId}/custom`, {
        method: "POST",
        body: {
          name,
          question,
          generation_instruction,
        },
      });
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["transparency_features", projectId] });
    },
  });
};

export const useUpdateCustomTransparencyFeature = (
  projectId: string,
  featureId: string
): UseMutationResult<CustomTransparencyFeatureResponse, Error, CustomTransparencyFeatureUpdate> => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      id,
      name,
      question,
      generation_instruction,
    }: CustomTransparencyFeatureUpdate) => {
      const response = await api<CustomTransparencyFeatureResponse>(
        `transparency_features/${projectId}/custom/${featureId}`,
        {
          method: "PUT",
          body: {
            id,
            name,
            question,
            generation_instruction,
          },
        }
      );
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["transparency_feature_data", projectId, featureId] });
      queryClient.invalidateQueries({ queryKey: ["transparency_features", projectId] });
    },
  });
};

export const useDeleteCustomTransparencyFeature = (
  projectId: string, featureId: string
): UseMutationResult<void, Error> => {
  return useMutation({
    mutationFn: async () => {
      await api<void>(`transparency_features/${projectId}/custom/${featureId}`, {
        method: "DELETE",
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["transparency_features", projectId] });
    },
  });
};

export const useRegenerateTransparencyFeatureData = (
  projectId: string,
  contractId: string,
  featureId: string
): UseMutationResult<TransparencyFeatureResultDTO, Error, void> => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      if (!projectId || !contractId || !featureId) {
        throw new Error("projectId, contractId, and featureId must be defined.");
      }
      return await api<TransparencyFeatureResultDTO>(
        `transparency_features/${projectId}/${contractId}/${featureId}/regenerate`,
        {
          method: "POST",
        }
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["transparency_feature_data", projectId, contractId, featureId],
      });
    },
  });
};
