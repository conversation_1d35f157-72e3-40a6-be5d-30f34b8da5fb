import { useMutation, useQuery } from "@tanstack/react-query";
import { api } from "./client";
import { components } from "./api-types";
import { queryClient } from "./query-client";

type FileDeduplicationResponseDTO = components["schemas"]["FileDeduplicationResponseDTO"];
type AnalyzeFilesResponse = components["schemas"]["AnalyzeFilesResponse"];
type TaskStatus = components["schemas"]["TaskStatus"];

export function useFileAnalysisDeduplication() {
    return useMutation<FileDeduplicationResponseDTO, Error, {files: FormData, projectId: string}>({
        mutationFn: async ({projectId, files} : {projectId: string, files: FormData}) => {
            return api<FileDeduplicationResponseDTO>(`file-analysis/${projectId}/deduplicate`, {
                method: "POST",
                body: files,
            });
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["file-analysis/deduplicate"] });
        },
    });
}

export const useFileAnalysis = (
    projectId: string | null,
) => {
    return useMutation<AnalyzeFilesResponse, Error, FormData>({
        mutationFn: async (files: FormData) => {
            return api<AnalyzeFilesResponse>(`file-analysis/${projectId}/analyse`, {
                method: "POST",
                body: files,
            });
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["file-analysis/analyse"] });
        },
    });
}

export const useFileAnalysisStatus = (
    taskId: string,
    options?: { enabled?: boolean; refetchInterval?: number }
) => {
    return useQuery<TaskStatus[]>({
        queryKey: ["task-status", taskId],
        queryFn: async () => {
            return await api<TaskStatus[]>(`file-analysis/status/${taskId}`, {
                method: "GET",
            })
        },
        enabled: options?.enabled ?? true,
        refetchInterval: options?.refetchInterval
    })
}
