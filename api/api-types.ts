/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Root */
        get: operations["root__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/health": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Health Check */
        get: operations["health_check_health_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/project/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get Projects */
        get: operations["get_projects_api_project__get"];
        put?: never;
        /** Create Project */
        post: operations["create_project_api_project__post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/project/{project_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get Project */
        get: operations["get_project_api_project__project_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/excel/status/{task_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Excelrouter.Get Excel Status
         * @description Get the status of an Excel generation task.
         */
        get: operations["ExcelRouter_get_excel_status_api_excel_status__task_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/excel/download/{task_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Excelrouter.Download Excel
         * @description Download the generated Excel file.
         */
        get: operations["ExcelRouter_download_excel_api_excel_download__task_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/excel/project/{project_id}/{contract_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Excelrouter.Request Contract Excel Generation
         * @description Request the generation of an Excel file for a project.
         */
        post: operations["ExcelRouter_request_contract_excel_generation_api_excel_project__project_id___contract_id__post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/excel/project/{project_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Excelrouter.Request Project Excel Generation
         * @description Request the generation of an Excel file for a project.
         */
        post: operations["ExcelRouter_request_project_excel_generation_api_excel_project__project_id__post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/chat/{project_id}/{contract_id}/ask_question": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Chatrouter.Post Message
         * @description Combined endpoint to ask a question and wait for the response.
         */
        post: operations["ChatRouter_post_message_api_chat__project_id___contract_id__ask_question_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/chat/{project_id}/{contract_id}/get_conversation": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Chatrouter.Get Conversation
         * @description GET /chat/conversation/{conversation_id}
         *     Retrieves the complete conversation transcript for the given conversation.
         */
        get: operations["ChatRouter_get_conversation_api_chat__project_id___contract_id__get_conversation_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/document/{project_id}/upload": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Documentrouter.Upload Files To S3
         * @description Listing endpoint that saves each file as a contract or attachment (as in the old service)
         *     and returns the list of saved documents along with any failed files.
         */
        post: operations["DocumentRouter_upload_files_to_s3_api_document__project_id__upload_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/document/{project_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Documentrouter.Get All Documents
         * @description Retrieve all documents (both Contracts and Attachments) for a given project.
         */
        get: operations["DocumentRouter_get_all_documents_api_document__project_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/document/{project_id}/ingest": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Documentrouter.Trigger Ingestion Pipeline
         * @description Trigger the ingestion pipeline for the given project, reading files from ./tmp/{project_id}.
         */
        post: operations["DocumentRouter_trigger_ingestion_pipeline_api_document__project_id__ingest_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/document/{project_id}/ingest-contract-with-attachments": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Documentrouter.Trigger Ingestion Pipeline Contract With Attachments
         * @description Trigger the ingestion pipeline for the given project
         */
        post: operations["DocumentRouter_trigger_ingestion_pipeline_contract_with_attachments_api_document__project_id__ingest_contract_with_attachments_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/transparency_features/static_data": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Transparencyfeaturerouter.Get Transparency Feature Static Data
         * @description Retrieve all standard (global) transparency features.
         *
         *     Returns:
         *         list[StandardTransparencyFeatureDTO]: A list of standard transparency features.
         */
        get: operations["TransparencyFeatureRouter_get_transparency_feature_static_data_api_transparency_features_static_data_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/transparency_features/{project_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Transparencyfeaturerouter.Get Transparency Features For Project And Contract
         * @description Retrieve the transparency features for a given project and contract.
         *
         *     This endpoint returns two separate lists:
         *      - standard_features: Global features (those not assigned to any project)
         *      - custom_features: Project-specific features (those assigned to the given project)
         *
         *     For custom features, the response includes additional fields:
         *      - extraction_question: The question used for LLM extraction
         *      - generation_instruction: The instruction used for LLM generation
         *      - sub_feature_id: The ID of the associated sub-feature
         *
         *     Args:
         *         project_id (UUID): The unique identifier of the project.
         *
         *     Returns:
         *         TransparencyFeaturesResponseDTO: An object containing separate lists for standard and custom features.
         */
        get: operations["TransparencyFeatureRouter_get_transparency_features_for_project_and_contract_api_transparency_features__project_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/transparency_features/{project_id}/{contract_id}/{transparency_feature_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Transparencyfeaturerouter.Get Transparency Feature Data
         * @description Retrieves or generates transparency feature data for a specific project and transparency feature.
         *
         *     This endpoint fetches transparency feature data based on the provided project ID and transparency feature ID.
         *     If extraction results already exist for the specified IDs, it returns the existing data. Otherwise, it
         *     generates new transparency feature data.
         *
         *     Args:
         *         project_id (UUID): The unique identifier of the project
         *         transparency_feature_id (UUID): The unique identifier of the transparency feature
         *
         *     Returns:
         *         TransparencyFeatureResultDTO: A data transfer object containing the transparency feature ID,
         *         name, and associated extraction results
         *
         *     Raises:
         *         HTTPException: If the transparency feature with the given ID doesn't exist (status code 404)
         */
        get: operations["TransparencyFeatureRouter_get_transparency_feature_data_api_transparency_features__project_id___contract_id___transparency_feature_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/transparency_features/{project_id}/{contract_id}/{transparency_feature_id}/regenerate": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Transparencyfeaturerouter.Regenerate Transparency Feature Data
         * @description Force Refresh generation of transparency feature data and benchmarking data for a specific project and transparency feature.
         *
         *     This endpoint fetches transparency feature data based on the provided project ID and transparency feature ID.
         *     It regenerates both the transparency feature data and the benchmarking data, storing them in the database.
         *
         *     Args:
         *         project_id (UUID): The unique identifier of the project
         *         transparency_feature_id (UUID): The unique identifier of the transparency feature
         *
         *     Returns:
         *         TransparencyFeatureResultDTO: A data transfer object containing the transparency feature ID,
         *         name, and associated extraction results
         *
         *     Raises:
         *         HTTPException: If the transparency feature with the given ID doesn't exist (status code 404)
         */
        post: operations["TransparencyFeatureRouter_regenerate_transparency_feature_data_api_transparency_features__project_id___contract_id___transparency_feature_id__regenerate_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/transparency_features/{project_id}/custom": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Transparencyfeaturerouter.Create Custom Transparency Feature */
        post: operations["TransparencyFeatureRouter_create_custom_transparency_feature_api_transparency_features__project_id__custom_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/transparency_features/{project_id}/custom/{feature_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /** Transparencyfeaturerouter.Update Custom Transparency Feature */
        put: operations["TransparencyFeatureRouter_update_custom_transparency_feature_api_transparency_features__project_id__custom__feature_id__put"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/transparency_features/{project_id}/custom/{transparency_feature_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        /** Transparencyfeaturerouter.Delete Custom Transparency Feature */
        delete: operations["TransparencyFeatureRouter_delete_custom_transparency_feature_api_transparency_features__project_id__custom__transparency_feature_id__delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/benchmarking/{project_id}/{contract_id}/{transparency_feature_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Benchmarkingrouter.Get Benchmarking Results */
        get: operations["BenchmarkingRouter_get_benchmarking_results_api_benchmarking__project_id___contract_id___transparency_feature_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/recommendations/{project_id}/{contract_id}/{transparency_feature_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Recommendationrouter.Get Recommendations */
        get: operations["RecommendationRouter_get_recommendations_api_recommendations__project_id___contract_id___transparency_feature_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/evaluation/{project_id}/evaluate": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Evaluationrouter.Evaluate
         * @description Evaluates the extraction results for a given project ID using the specified Excel file and sheet name.
         */
        post: operations["EvaluationRouter_evaluate_api_evaluation__project_id__evaluate_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/file-analysis/{project_id}/deduplicate": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Fileanalysisrouter.Deduplicate Files */
        post: operations["FileAnalysisRouter_deduplicate_files_api_file_analysis__project_id__deduplicate_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/file-analysis/{project_id}/analyse": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Fileanalysisrouter.Analyze Files
         * @description Endpoint to analyze uploaded files.
         *
         *     This endpoint accepts a list of files, reads their contents into memory,
         *     and queues a background task to perform file analysis. It returns a task ID
         *     and the initial status of the task.
         *
         *     Args:
         *         background_tasks (BackgroundTasks): FastAPI's background task manager
         *             to schedule the file analysis task. This is not passed by the FE,
         *             but actually managed by FastAPI
         *         files (List[UploadFile]): A list of files to be analyzed. Each file is
         *             uploaded as part of the request.
         *
         *     Returns:
         *         AnalyzeFilesResponse: A response object containing the task ID and the
         *         initial status of the task (QUEUED).
         *
         *     Raises:
         *         HTTPException: If there is an issue with file upload or processing.
         *
         *     Notes:
         *         - The files are read into memory before being passed to the background task.
         *         - The task status is initialized as QUEUED and stored in the TASKS dictionary.
         *         - The actual file analysis is performed asynchronously in the background.
         */
        post: operations["FileAnalysisRouter_analyze_files_api_file_analysis__project_id__analyse_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/file-analysis/status/{task_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Fileanalysisrouter.Get Analysis Status */
        get: operations["FileAnalysisRouter_get_analysis_status_api_file_analysis_status__task_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/presentations/project/{project_id}/{contract_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Presentationrouter.Request Project Presentation
         * @description Request the generation of a presentation for a project.
         */
        post: operations["PresentationRouter_request_project_presentation_api_presentations_project__project_id___contract_id__post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/presentations/status/{task_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Presentationrouter.Check Presentation Status
         * @description Check the status of a presentation generation task.
         */
        get: operations["PresentationRouter_check_presentation_status_api_presentations_status__task_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/presentations/download/{task_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Presentationrouter.Download Presentation
         * @description Download a generated presentation if it's ready.
         */
        get: operations["PresentationRouter_download_presentation_api_presentations_download__task_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/contracts/{contract_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Contractrouter.Get Contract */
        get: operations["ContractRouter_get_contract_api_contracts__contract_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/statistics/extractions/{project_id}/{contract_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Statisticsrouter.Get Extraction Statistics
         * @description Get statistics about extraction results for a specific project and contract.
         *
         *     Args:
         *         project_id: The UUID of the project
         *         contract_id: The UUID of the contract
         *
         *     Returns:
         *         Dictionary containing statistics about extraction results
         */
        get: operations["StatisticsRouter_get_extraction_statistics_api_statistics_extractions__project_id___contract_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/statistics/benchmarks/{project_id}/{contract_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Statisticsrouter.Get Benchmark Statistics
         * @description Get compliance statistics for all transparency features for a specific project and contract.
         *
         *     This endpoint aggregates the compliance statuses of all subfeatures across
         *     all transparency features, returning counts in each compliance category.
         *
         *     Args:
         *         project_id: The UUID of the project
         *         contract_id: The UUID of the contract
         *
         *     Returns:
         *         BenchmarkStatisticsDTO containing counts of subfeatures in each compliance category:
         *         - compliant: Number of subfeatures marked as "Compliant"
         *         - partially_compliant: Number of subfeatures marked as "Partially Compliant"
         *         - non_compliant: Number of subfeatures marked as "Non Compliant"
         *         - not_available: Number of subfeatures with unknown compliance status
         */
        get: operations["StatisticsRouter_get_benchmark_statistics_api_statistics_benchmarks__project_id___contract_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        /** AnalyzeFilesResponse */
        AnalyzeFilesResponse: {
            /** Task Id */
            task_id: string;
            status: components["schemas"]["TaskStatusEnum"];
        };
        /** Attachment */
        Attachment: {
            /** File Name */
            file_name: string;
            /** File Size */
            file_size: number;
        };
        /** AttachmentMappingDTO */
        AttachmentMappingDTO: {
            /** Attachment File Name */
            attachment_file_name: string;
            /** Confidence */
            confidence: number;
            /**
             * Is Existing
             * @default false
             */
            is_existing: boolean;
            /**
             * Is Replaced
             * @default false
             */
            is_replaced: boolean;
        };
        /**
         * AuthorRoleDTO
         * @enum {string}
         */
        AuthorRoleDTO: "user" | "assistant";
        /** BenchmarkResultDTO */
        BenchmarkResultDTO: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Sub Feature Name */
            sub_feature_name: string;
            /** Compliance Status */
            compliance_status: string;
            /** Reason */
            reason: string;
        };
        /** BenchmarkResultsDTO */
        BenchmarkResultsDTO: {
            /** Summary */
            summary: string;
            /** Benchmark Results */
            benchmark_results: components["schemas"]["BenchmarkResultDTO"][];
        };
        /** BenchmarkStatisticsDTO */
        BenchmarkStatisticsDTO: {
            /** Compliant */
            compliant: number;
            /** Partially Compliant */
            partially_compliant: number;
            /** Non Compliant */
            non_compliant: number;
            /** Not Available */
            not_available: number;
        };
        /** Body_DocumentRouter_upload_files_to_s3_api_document__project_id__upload_post */
        Body_DocumentRouter_upload_files_to_s3_api_document__project_id__upload_post: {
            /**
             * Files
             * @default []
             */
            files: string[];
        };
        /** Body_FileAnalysisRouter_analyze_files_api_file_analysis__project_id__analyse_post */
        Body_FileAnalysisRouter_analyze_files_api_file_analysis__project_id__analyse_post: {
            /**
             * Files
             * @default []
             */
            files: string[];
            /** @default {
             *       "confidence_threshold_contract": 0.85,
             *       "confidence_threshold_attachment_to_contract_mapping": 0.85,
             *       "max_llm_retries": 5,
             *       "task_cleanup_delay_seconds": 3600,
             *       "classification_batch_size": 5,
             *       "max_parallel_batches": 5,
             *       "duplicate_similarity_threshold": 90,
             *       "number_characters_contract_file": 40000
             *     } */
            file_analysis_params: components["schemas"]["FileAnalysisParams"];
        };
        /** Body_FileAnalysisRouter_deduplicate_files_api_file_analysis__project_id__deduplicate_post */
        Body_FileAnalysisRouter_deduplicate_files_api_file_analysis__project_id__deduplicate_post: {
            /** Files */
            files: string[];
            /** @default {
             *       "confidence_threshold_contract": 0.85,
             *       "confidence_threshold_attachment_to_contract_mapping": 0.85,
             *       "max_llm_retries": 5,
             *       "task_cleanup_delay_seconds": 3600,
             *       "classification_batch_size": 5,
             *       "max_parallel_batches": 5,
             *       "duplicate_similarity_threshold": 90,
             *       "number_characters_contract_file": 40000
             *     } */
            file_analysis_params: components["schemas"]["FileAnalysisParams"];
        };
        /** ChatMessageResponse */
        ChatMessageResponse: {
            /** Response */
            response: string;
        };
        /** ClusterDTO */
        ClusterDTO: {
            /** Cluster Id */
            cluster_id: number;
            /** Files */
            files: components["schemas"]["FileDTO"][];
        };
        /** ConversationResponse */
        ConversationResponse: {
            /**
             * Conversation Id
             * Format: uuid
             */
            conversation_id: string;
            /** Messages */
            messages: components["schemas"]["MessageDTO"][] | null;
        };
        /** CreateProjectRequest */
        CreateProjectRequest: {
            /** Name */
            name: string;
            /** Client */
            client: string | null;
            /** Case Code */
            case_code: string | null;
            /** Description */
            description: string | null;
        };
        /** CustomTransparencyFeatureCreate */
        CustomTransparencyFeatureCreate: {
            /** Name */
            name: string;
            /** Question */
            question: string;
            /** Generation Instruction */
            generation_instruction?: string | null;
        };
        /**
         * CustomTransparencyFeatureDTO
         * @description DTO for custom (project-specific) transparency features.
         */
        CustomTransparencyFeatureDTO: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Feature Name */
            feature_name: string;
            /** Description */
            description?: string | null;
            /** Extraction Question */
            extraction_question?: string | null;
            /** Generation Instruction */
            generation_instruction?: string | null;
            /** Sub Feature Id */
            sub_feature_id?: string | null;
        };
        /** CustomTransparencyFeatureResponse */
        CustomTransparencyFeatureResponse: {
            /**
             * Feature Id
             * Format: uuid
             */
            feature_id: string;
            /**
             * Sub Feature Id
             * Format: uuid
             */
            sub_feature_id: string;
            /** Message */
            message: string;
        };
        /** CustomTransparencyFeatureUpdate */
        CustomTransparencyFeatureUpdate: {
            /** Name */
            name: string;
            /** Question */
            question: string;
            /** Generation Instruction */
            generation_instruction?: string | null;
            /**
             * Id
             * Format: uuid
             */
            id: string;
        };
        /** DocumentDTO */
        DocumentDTO: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Name */
            name: string;
            /** File Path */
            file_path: string;
            /**
             * Contract Id
             * Format: uuid
             */
            contract_id: string;
            /**
             * Project Id
             * Format: uuid
             */
            project_id: string;
            status: components["schemas"]["DocumentStatus"];
            /**
             * Uploaded At
             * Format: date-time
             */
            uploaded_at: string;
            document_format: components["schemas"]["DocumentFormat"];
            document_type: components["schemas"]["DocumentType"];
            /** Document Size */
            document_size: number;
        };
        /**
         * DocumentFormat
         * @enum {string}
         */
        DocumentFormat: "DOCUMENT" | "SPREADSHEET" | "TEXT" | "ATTACHMENT" | "PRESENTATION" | "OTHER";
        /**
         * DocumentStatus
         * @enum {string}
         */
        DocumentStatus: "IDLE" | "PROCESSED" | "PENDING" | "ERROR";
        /**
         * DocumentType
         * @enum {string}
         */
        DocumentType: "CONTRACT" | "EXHIBIT" | "SCHEDULE" | "ANNEX" | "APPENDIX" | "AGREEMENT" | "AMENDMENT" | "ADDENDUM" | "ATTACHMENT" | "OTHER";
        /** ExcelTaskStatus */
        ExcelTaskStatus: {
            status: components["schemas"]["TaskStatusEnum"];
            /** File Path */
            file_path?: string | null;
            /** Error */
            error?: string | null;
        };
        /** ExtractionResultDTO */
        ExtractionResultDTO: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Name */
            name: string;
            /** Extracted Text */
            extracted_text: string;
        };
        /** ExtractionStatisticsDTO */
        ExtractionStatisticsDTO: {
            /** With Data */
            with_data: number;
            /** Total Sub Features */
            total_sub_features: number;
        };
        /** FileAnalysisDTO */
        FileAnalysisDTO: {
            /** Contract Data Results */
            contract_data_results: components["schemas"]["VendorContractsDTO"][];
            /** Unmapped Attachments */
            unmapped_attachments: string[];
        };
        /**
         * FileAnalysisParams
         * @description Parameters for the File Analysis.
         */
        FileAnalysisParams: {
            /**
             * Confidence Threshold Contract
             * @description Confidence threshold to consider a file as a contract
             * @default 0.85
             */
            confidence_threshold_contract: number;
            /**
             * Confidence Threshold Attachment To Contract Mapping
             * @description Confidence threshold to map an attachment to a contract
             * @default 0.85
             */
            confidence_threshold_attachment_to_contract_mapping: number;
            /**
             * Max Llm Retries
             * @description Maximum number of retries for LLM calls
             * @default 5
             */
            max_llm_retries: number;
            /**
             * Task Cleanup Delay Seconds
             * @description Delay in seconds before cleaning up a task
             * @default 3600
             */
            task_cleanup_delay_seconds: number;
            /**
             * Classification Batch Size
             * @description Batch size for file classification
             * @default 5
             */
            classification_batch_size: number;
            /**
             * Max Parallel Batches
             * @description Maximal number of parallel calls made to OpenAI API
             * @default 5
             */
            max_parallel_batches: number;
            /**
             * Duplicate Similarity Threshold
             * @description Similarity threshold for considering files as duplicates
             * @default 90
             */
            duplicate_similarity_threshold: number;
            /**
             * Number Characters Contract File
             * @description Number of characters from a contract file sent to a LLM to identify contract information
             * @default 40000
             */
            number_characters_contract_file: number;
        };
        /** FileDTO */
        FileDTO: {
            /** File Name */
            file_name: string;
            /** File Size Kb */
            file_size_kb: number;
            /** Score */
            score: number;
            /** Selected */
            selected: boolean;
            /**
             * Is Existing
             * @default false
             */
            is_existing: boolean;
        };
        /** FileDeduplicationResponseDTO */
        FileDeduplicationResponseDTO: {
            /** Status */
            status: string;
            /** Deduplication Summary */
            deduplication_summary: {
                [key: string]: number;
            };
            /** Clusters */
            clusters: components["schemas"]["ClusterDTO"][];
        };
        /** HTTPValidationError */
        HTTPValidationError: {
            /** Detail */
            detail?: components["schemas"]["ValidationError"][];
        };
        /** IngestionRequestDTO */
        IngestionRequestDTO: {
            /** Providers */
            providers: components["schemas"]["ProviderData"][];
        };
        /** IngestionResponse */
        IngestionResponse: {
            /** Message */
            message: string;
        };
        /**
         * IngestionStatus
         * @enum {string}
         */
        IngestionStatus: "idle" | "in_progress" | "success" | "failed";
        /** MainContract */
        MainContract: {
            /** File Name */
            file_name: string;
            /** File Size */
            file_size: number;
            /** Start Date */
            start_date: string;
            /** End Date */
            end_date: string;
            /**
             * Contract Value
             * @default
             */
            contract_value: string;
            /** Contract Type */
            contract_type: string;
            /** Attachments */
            attachments: components["schemas"]["Attachment"][];
        };
        /** MessageDTO */
        MessageDTO: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            author: components["schemas"]["AuthorRoleDTO"];
            /** Content */
            content: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
        };
        /** MessageRequest */
        MessageRequest: {
            /** Question */
            question: string;
        };
        /** PresentationResponse */
        PresentationResponse: {
            /** Task Id */
            task_id: string;
            status: components["schemas"]["TaskStatusEnum"];
        };
        /** PresentationTaskStatus */
        PresentationTaskStatus: {
            status: components["schemas"]["TaskStatusEnum"];
            /** File Path */
            file_path?: string | null;
            /** Error */
            error?: string | null;
            /** Project Name */
            project_name?: string | null;
        };
        /** ProjectDTO */
        ProjectDTO: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Name */
            name: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /** Client */
            client: string | null;
            /** Case Code */
            case_code: string | null;
            /** Description */
            description: string | null;
            ingestion_status: components["schemas"]["IngestionStatus"] | null;
        };
        /** ProviderDTO */
        ProviderDTO: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Name */
            name: string;
        };
        /** ProviderData */
        ProviderData: {
            /** Providers */
            providers: string;
            /** Main Contracts */
            main_contracts: components["schemas"]["MainContract"][];
        };
        /** RecommendationResultDTO */
        RecommendationResultDTO: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Sub Feature Name */
            sub_feature_name: string;
            /** Recommendation */
            recommendation: string;
        };
        /** RecommendationResultsDTO */
        RecommendationResultsDTO: {
            /** Summary */
            summary: string;
            /** Recommendation Result */
            recommendation_result: components["schemas"]["RecommendationResultDTO"][];
        };
        /**
         * StandardTransparencyFeatureDTO
         * @description DTO for standard (global) transparency features.
         */
        StandardTransparencyFeatureDTO: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Feature Name */
            feature_name: string;
            /** Description */
            description?: string | null;
        };
        /** TaskStatus */
        TaskStatus: {
            status: components["schemas"]["TaskStatusEnum"];
            /**
             * Project Id
             * Format: uuid
             */
            project_id: string;
            result?: components["schemas"]["FileAnalysisDTO"] | null;
            /** Error */
            error?: string | null;
        };
        /**
         * TaskStatusEnum
         * @enum {string}
         */
        TaskStatusEnum: "queued" | "processing" | "completed" | "failed";
        /** TransparencyFeatureResultDTO */
        TransparencyFeatureResultDTO: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Name */
            name: string;
            /** Summary */
            summary: string;
            /** Extraction Results */
            extraction_results: components["schemas"]["ExtractionResultDTO"][];
        };
        /**
         * TransparencyFeaturesResponseDTO
         * @description Response DTO containing separate lists for standard and custom transparency features.
         */
        TransparencyFeaturesResponseDTO: {
            /** Standard Features */
            standard_features: components["schemas"]["StandardTransparencyFeatureDTO"][];
            /** Custom Features */
            custom_features: components["schemas"]["CustomTransparencyFeatureDTO"][];
        };
        /** ValidationError */
        ValidationError: {
            /** Location */
            loc: (string | number)[];
            /** Message */
            msg: string;
            /** Error Type */
            type: string;
        };
        /** VendorContractsDTO */
        VendorContractsDTO: {
            /** Vendors */
            vendors: string[];
            /** Main Contracts */
            main_contracts: components["schemas"]["app__api__dtos__file_analysis_dto__ContractDTO"][];
        };
        /** ContractDTO */
        app__api__dtos__contract_dto__ContractDTO: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Name */
            name: string;
            /** File Path */
            file_path: string;
            /**
             * Project Id
             * Format: uuid
             */
            project_id: string;
            /**
             * Provider Id
             * Format: uuid
             */
            provider_id: string;
            /** Type */
            type?: string | null;
            /** Start Date */
            start_date?: string | null;
            /** End Date */
            end_date?: string | null;
            provider: components["schemas"]["ProviderDTO"];
        };
        /** ContractDTO */
        app__api__dtos__file_analysis_dto__ContractDTO: {
            /** File Name */
            file_name: string;
            /**
             * Contract Type
             * @enum {string}
             */
            contract_type: "AMS - Application Management Services" | "ADM - Application Development and Maintenance" | "Infrastructure" | "Support Services" | "Other";
            /** Start Date */
            start_date: string;
            /** End Date */
            end_date: string;
            /** Confidence Score */
            confidence_score: number;
            /** Reasoning */
            reasoning: string;
            /** Attachments */
            attachments: components["schemas"]["AttachmentMappingDTO"][];
            /**
             * Is Existing
             * @default false
             */
            is_existing: boolean;
            /**
             * Is Replaced
             * @default false
             */
            is_replaced: boolean;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    root__get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    health_check_health_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    get_projects_api_project__get: {
        parameters: {
            query?: never;
            header: {
                "X-User-Id": string;
            };
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ProjectDTO"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    create_project_api_project__post: {
        parameters: {
            query?: never;
            header: {
                "X-User-Id": string;
            };
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CreateProjectRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_project_api_project__project_id__get: {
        parameters: {
            query?: never;
            header: {
                "X-User-Id": string;
            };
            path: {
                project_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ProjectDTO"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    ExcelRouter_get_excel_status_api_excel_status__task_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description The task ID */
                task_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ExcelTaskStatus"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    ExcelRouter_download_excel_api_excel_download__task_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description The task ID */
                task_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    ExcelRouter_request_contract_excel_generation_api_excel_project__project_id___contract_id__post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description The ID of the project */
                project_id: string;
                /** @description The ID of the contract */
                contract_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PresentationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    ExcelRouter_request_project_excel_generation_api_excel_project__project_id__post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description The ID of the project */
                project_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PresentationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    ChatRouter_post_message_api_chat__project_id___contract_id__ask_question_post: {
        parameters: {
            query?: never;
            header: {
                "X-User-Id": string;
            };
            path: {
                project_id: string;
                contract_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MessageRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ChatMessageResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    ChatRouter_get_conversation_api_chat__project_id___contract_id__get_conversation_get: {
        parameters: {
            query?: never;
            header: {
                "X-User-Id": string;
            };
            path: {
                project_id: string;
                contract_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ConversationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    DocumentRouter_upload_files_to_s3_api_document__project_id__upload_post: {
        parameters: {
            query?: {
                input_folder?: string;
                output_folder?: string;
                embedding_model?: string;
                embedding_batch_size?: number;
                openai_api_delay_in_calls?: number;
                db_storing_batch_size?: number;
                llm_model?: string;
            };
            header?: never;
            path: {
                project_id: string;
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                "multipart/form-data": components["schemas"]["Body_DocumentRouter_upload_files_to_s3_api_document__project_id__upload_post"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string[];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    DocumentRouter_get_all_documents_api_document__project_id__get: {
        parameters: {
            query?: {
                input_folder?: string;
                output_folder?: string;
                embedding_model?: string;
                embedding_batch_size?: number;
                openai_api_delay_in_calls?: number;
                db_storing_batch_size?: number;
                llm_model?: string;
            };
            header?: never;
            path: {
                project_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DocumentDTO"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    DocumentRouter_trigger_ingestion_pipeline_api_document__project_id__ingest_post: {
        parameters: {
            query?: {
                input_folder?: string;
                output_folder?: string;
                embedding_model?: string;
                embedding_batch_size?: number;
                openai_api_delay_in_calls?: number;
                db_storing_batch_size?: number;
                llm_model?: string;
            };
            header?: never;
            path: {
                project_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["IngestionResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    DocumentRouter_trigger_ingestion_pipeline_contract_with_attachments_api_document__project_id__ingest_contract_with_attachments_post: {
        parameters: {
            query?: {
                input_folder?: string;
                output_folder?: string;
                embedding_model?: string;
                embedding_batch_size?: number;
                openai_api_delay_in_calls?: number;
                db_storing_batch_size?: number;
                llm_model?: string;
            };
            header?: never;
            path: {
                project_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["IngestionRequestDTO"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["IngestionResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    TransparencyFeatureRouter_get_transparency_feature_static_data_api_transparency_features_static_data_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["StandardTransparencyFeatureDTO"][];
                };
            };
        };
    };
    TransparencyFeatureRouter_get_transparency_features_for_project_and_contract_api_transparency_features__project_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                project_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TransparencyFeaturesResponseDTO"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    TransparencyFeatureRouter_get_transparency_feature_data_api_transparency_features__project_id___contract_id___transparency_feature_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                project_id: string;
                contract_id: string;
                transparency_feature_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TransparencyFeatureResultDTO"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    TransparencyFeatureRouter_regenerate_transparency_feature_data_api_transparency_features__project_id___contract_id___transparency_feature_id__regenerate_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                project_id: string;
                contract_id: string;
                transparency_feature_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TransparencyFeatureResultDTO"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    TransparencyFeatureRouter_create_custom_transparency_feature_api_transparency_features__project_id__custom_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                project_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CustomTransparencyFeatureCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CustomTransparencyFeatureResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    TransparencyFeatureRouter_update_custom_transparency_feature_api_transparency_features__project_id__custom__feature_id__put: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                project_id: string;
                feature_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CustomTransparencyFeatureUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CustomTransparencyFeatureResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    TransparencyFeatureRouter_delete_custom_transparency_feature_api_transparency_features__project_id__custom__transparency_feature_id__delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                project_id: string;
                transparency_feature_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": Record<string, never>;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    BenchmarkingRouter_get_benchmarking_results_api_benchmarking__project_id___contract_id___transparency_feature_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                project_id: string;
                contract_id: string;
                transparency_feature_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["BenchmarkResultsDTO"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    RecommendationRouter_get_recommendations_api_recommendations__project_id___contract_id___transparency_feature_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                project_id: string;
                contract_id: string;
                transparency_feature_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RecommendationResultsDTO"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    EvaluationRouter_evaluate_api_evaluation__project_id__evaluate_post: {
        parameters: {
            query: {
                excel_file: string;
                sheet_name: string;
            };
            header?: never;
            path: {
                project_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    FileAnalysisRouter_deduplicate_files_api_file_analysis__project_id__deduplicate_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                project_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "multipart/form-data": components["schemas"]["Body_FileAnalysisRouter_deduplicate_files_api_file_analysis__project_id__deduplicate_post"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["FileDeduplicationResponseDTO"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    FileAnalysisRouter_analyze_files_api_file_analysis__project_id__analyse_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                project_id: string;
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                "multipart/form-data": components["schemas"]["Body_FileAnalysisRouter_analyze_files_api_file_analysis__project_id__analyse_post"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AnalyzeFilesResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    FileAnalysisRouter_get_analysis_status_api_file_analysis_status__task_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                task_id: string;
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["FileAnalysisParams"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskStatus"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    PresentationRouter_request_project_presentation_api_presentations_project__project_id___contract_id__post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description The ID of the project */
                project_id: string;
                /** @description The ID of the contract */
                contract_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PresentationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    PresentationRouter_check_presentation_status_api_presentations_status__task_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                task_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PresentationTaskStatus"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    PresentationRouter_download_presentation_api_presentations_download__task_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                task_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    ContractRouter_get_contract_api_contracts__contract_id__get: {
        parameters: {
            query?: never;
            header: {
                "X-User-Id": string;
            };
            path: {
                contract_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["app__api__dtos__contract_dto__ContractDTO"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    StatisticsRouter_get_extraction_statistics_api_statistics_extractions__project_id___contract_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                project_id: string;
                contract_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ExtractionStatisticsDTO"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    StatisticsRouter_get_benchmark_statistics_api_statistics_benchmarks__project_id___contract_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                project_id: string;
                contract_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["BenchmarkStatisticsDTO"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
}
