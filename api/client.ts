import { getApiKey, getApiUrl } from "@/lib/runtime-environment";
import { getIdentity } from "../utils/user";
import { getSession } from "next-auth/react";


type HttpMethod = "GET" | "POST" | "PATCH" | "PUT" | "DELETE";

type ApiConfig<TData> = {
  body?: TData;
  method?: HttpMethod;
  headers?: HeadersInit;
  returnRawResponse?: boolean;
} & Omit<RequestInit, "body" | "method" | "headers">;

export async function api<TResponse, TData = unknown>(
  endpoint: string,
  { body, method, returnRawResponse = false, ...customConfig }: ApiConfig<TData> = {}
): Promise<TResponse> {
  if (!endpoint) {
    throw new Error("Endpoint is required");
  }
  const session = await getSession();
  const token = session?.accessToken;

  const isFormData = body instanceof FormData;

  const headers: HeadersInit = {
    "X-User-Id": getIdentity(session),
    "x-api-key": getApiKey(),
    ...customConfig.headers,
    ...(isFormData ? {} : { "Content-Type": "application/json" }),
    ...(token ? { Authorization: `Bearer ${token}` } : {}),
      };

  const config: RequestInit = {
    method: method ?? (body ? "POST" : "GET"),
    ...customConfig,
    headers,
  };

  if (body) {
    config.body = isFormData ? (body as FormData) : JSON.stringify(body);
  }

  const response = await fetch(`${getApiUrl()}${endpoint}`, config);
  if (returnRawResponse) {
    return response as any;
  }

  if (response.ok) {
    const contentType = response.headers.get("content-type");
    if (contentType?.includes("application/json")) {
      const contentLength = response.headers.get("content-length");
      if (contentLength && parseInt(contentLength) > 0) {
        return await response.json();
      }
    }
    return {} as TResponse;
  }

  throw new Error(await response.text());
}
