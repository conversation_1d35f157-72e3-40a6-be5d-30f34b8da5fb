import { useMutation, UseMutationResult, useQueryClient } from "@tanstack/react-query";
import { api } from "./client";
import { components } from "./api-types";
import { useQuery } from "@tanstack/react-query";

type ProjectDTO = components["schemas"]["ProjectDTO"];
type CreateProjectRequest = components["schemas"]["CreateProjectRequest"];

export const useCreateProject = (): UseMutationResult<string, Error, CreateProjectRequest> => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({
            name,
            client,
            case_code,
            description
        }: CreateProjectRequest) => {
            const projectId = await api<string>("project/", {
                method: "POST",
                body: {
                    name,
                    client,
                    case_code,
                    description
                }
            });
            return projectId;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["projects"] });
        },
    });
};

export const useProjects = () => {
    return useQuery<ProjectDTO[]>({
        queryKey: ["projects"],
        queryFn: async () => {
            const projects = await api<ProjectDTO[]>("project/", {
                method: "GET",
            });
            return projects;
        }
    });
};

export const useProject = (
    projectId: string,
) => {
    return useQuery<ProjectDTO>({
        queryKey: ["projects", projectId],
        queryFn: async () => {
            const project = await api<ProjectDTO>(`project/${projectId}`, {
                method: "GET",
            });
            return project;
        }
    });
};

export const fetchProjects = async (): Promise<ProjectDTO[]> => {
    return api<ProjectDTO[]>("project/", { method: "GET" });
};
