import { useQuery } from "@tanstack/react-query"
import { api } from "./client"
import { components } from "./api-types"
import { useMutation, useQueryClient } from "@tanstack/react-query"

type ConversationResponse = components["schemas"]["ConversationResponse"];

export function useConversation(projectId: string, contractId: string) {
  return useQuery<ConversationResponse>({
    queryKey: ["conversation", projectId, contractId],
    queryFn: async () => {
      return api<ConversationResponse>(
        `chat/${projectId}/${contractId}/get_conversation`,
        { method: "GET" }
      )
    },
    enabled: Boolean(projectId),
  })
}

type MessageResponse = components["schemas"]["MessageResponse"];
type MessageRequest = components["schemas"]["MessageRequest"];

export function useSendMessage(projectId: string, contractId: string) {
  const queryClient = useQueryClient()

  return useMutation<
    MessageResponse,
    Error,
    MessageRequest
  >({
    mutationFn: async (data: MessageRequest) => {
      return api<MessageResponse>(`chat/${projectId}/${contractId}/ask_question`, {
        method: "POST",
        body: data,
      })
    },
    onSuccess: () => {
      if (projectId) {
        queryClient.invalidateQueries({ queryKey: ["conversation", projectId, contractId] })
      }
    },
  })
}
