import { useQuery } from "@tanstack/react-query";
import { api } from "./client";
import { components } from "./api-types";

type BenchmarkResultsDTO = components["schemas"]["BenchmarkResultsDTO"];

export function useGetBenchmarkData(project_id: string, contract_id: string, transparency_feature_id: string) {
    return useQuery<BenchmarkResultsDTO, Error>({
        queryKey: ["benchmarking", project_id, transparency_feature_id],
        queryFn: async () => {
            return api<BenchmarkResultsDTO>(`benchmarking/${project_id}/${contract_id}/${transparency_feature_id}`, {
                method: "GET",
            });

        },
    });
}