import { api } from "./client"
import { components } from "./api-types"
import { useQuery } from "@tanstack/react-query"

type ExtractionStatistics = components["schemas"]["ExtractionStatisticsDTO"]
type BenchmarkStatistics = components["schemas"]["BenchmarkStatisticsDTO"]

export function useGetExtractionStatistics(
    projectId: string,
    contractId: string,
) {
    return useQuery<ExtractionStatistics, Error>({
        queryKey: ["extraction-statistics", projectId, contractId],
        queryFn: async () => {
            return api<ExtractionStatistics>(`statistics/extractions/${projectId}/${contractId}`, {
                method: "GET",
            });
        },
    });
}

export function useGetBenchmarkStatistics(
    projectId: string,
    contractId: string
) {
    return useQuery<BenchmarkStatistics, Error>({
        queryKey: ["benchmark-statistics", projectId, contractId],
        queryFn: async () => {
            return api<BenchmarkStatistics>(`statistics/benchmarks/${projectId}/${contractId}`, {
                method: "GET",
            });
        }
    })
}