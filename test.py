#!/usr/bin/env python3

class Service:
    def serve(self):
        print("Service is now providing the required functionality.")

class Client:
    def __init__(self, service: Service):
        # The service is injected when the client is instantiated
        self.service = service

    def do_something(self):
        print("Client is using the injected service...")
        self.service.serve()

if __name__ == "__main__":
    # Create an instance of Service
    service_instance = Service()
    # Inject the service instance into the Client
    client_instance = Client(service_instance)
    client_instance.do_something()
