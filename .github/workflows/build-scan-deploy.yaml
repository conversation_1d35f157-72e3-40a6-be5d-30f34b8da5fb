name: build-scan-deploy-pipeline

on:
  push:
    branches:
      - main
      - cert-update

  workflow_dispatch:
    inputs:
      Branch:
        description: "Branch to Build"
        required: true
        default: "main"
jobs:
  SMP-Dotnet-Deploy-Workflow:
    uses: bcg-cpe-devsecops-shared/github-action-reusable-workflows/.github/workflows/smp-node-workflow-v2.yaml@main
    with:
      # Service and Checkout variables
      service_name: "tech-cost-takeout-app"
      branch: ${{ github.event.inputs.Branch }}

      # Release Branch Variables
      release_branch: main
      run_number: ${{ github.run_number }}

      # Node/npm variables
      node_version: "20.x"
      run_tests: false

      # Veracode Variables
      veracodescan_enabled: false
      veracode_fullscan_enabled: true
      veracode_pipelinescan_enabled: false
      veracode_app_name: ""

      # Sonarqube Variables
      sonar_project_name: "tech-cost-takeout-app"

      # Docker Variables
      docker_registry: "bcgprod.jfrog.io"
      docker_repo: "bcg-cpe-x-gpttechcosttakeout-docker-virtual"

      # Helm Variables
      helm_repo_url: "https://bcgprod.jfrog.io/artifactory/bcg-cpe-x-GPTTechCostTakeout-helm-virtual"
      helm_chart_name: "tech-cost-takeout-app"
      helm_chart_dir: "helmchart"

      # Octopus Variables
      octopus_account_id: "11a9a108-44b1-4a05-91fd-e5e0986e24d3"
      octopus_space: "SharedApps"
      octopus_project_group: "Tech Cost Takeout"
      octopus_project: "Tech Cost Takeout App"
      octopus_prerelease_lifecycle: "sharedapps-prerelease-lifecycle"
      octopus_release_lifecycle: "sharedapps-release-lifecycle"
      octopus_deployment_package: "Deploy Helm Upgrade/ValuesPack-1"
      octopus_worker_pool: "SMP STATIC HOSTED UBUNTU WORKER POOL HELM-3-3-4"
      octopus_helm_template: "SMP-Deploy-Helm3-Upgrades-Linux"

    # AKV Secret Variables
    secrets:
      azure_secret: ${{ secrets.AKV_CPE_XGPT_CI_SECRET }}
      vault_name: ${{ secrets.AKV_CPE_XGPT_CI_VAULT_NAME }}
