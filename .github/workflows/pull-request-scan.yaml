name: pull-request-scan-pipeline

on:
  pull_request:
    branches:
      - main

jobs:
  SMP-NodeJs-Scan-Workflow:
    uses: bcg-cpe-devsecops-shared/github-action-reusable-workflows/.github/workflows/smp-node-scan-workflow-v2.yaml@main
    with:
      # Service and Checkout variables
      service_name: "tech-cost-takeout-app"
      branch: ${{ github.event.inputs.Branch }}

      # Release Branch Variables
      run_number: ${{ github.run_number }}

      # Node/npm variables
      node_version: "20.x"
      run_tests: false
      
      # Veracode Variables
      veracodescan_enabled: false
      veracode_fullscan_enabled: true
      veracode_pipelinescan_enabled: false
      veracode_app_name: ""

      # Sonarqube Variables
      sonar_project_name: "tech-cost-takeout-app"

      # Docker Variables
      docker_registry: "bcgprod.jfrog.io"
      docker_repo: "bcg-cpe-x-gpttechcosttakeout-docker-virtual"


    # AKV Secret Variables  
    secrets:
      azure_secret: ${{ secrets.AKV_CPE_XGPT_CI_SECRET }}
      vault_name: ${{ secrets.AKV_CPE_XGPT_CI_VAULT_NAME }}