# 🧱 Builder image
FROM node:22-slim AS builder

WORKDIR /app

# Copy only dependency files first (for better layer caching)
COPY package.json yarn.lock ./

# Install dependencies
RUN yarn install #--frozen-lockfile

# Copy rest of the app
COPY . .

# Build Next.js app
RUN yarn build

# 🏃 Runtime image
FROM node:22-alpine AS runner

WORKDIR /app

ENV NODE_ENV=production

# Install only production dependencies
COPY package.json yarn.lock ./
RUN yarn install #--production --frozen-lockfile

# Copy built output from builder
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public


# Expose app port
EXPOSE 3000

# Start the app
CMD ["yarn", "start"]
