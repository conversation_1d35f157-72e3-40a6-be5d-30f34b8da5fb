import type { Config } from "tailwindcss"
const config: Config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
          light: "#00FFCC", // Bright Teal
          medium: "#00E0B5", // Medium Teal
          shade: "#00A887", // Shade Teal
          dark: "#02786E", // Dark Teal
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
          light: "#82F5F5", // Bright Cerulean
          medium: "#14E6E6", // Medium Cerulean
          dark: "#008E99", // Dark Cerulean
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
          DEFAULT: "#E4E4E9", // Light Grey
          foreground: "#67676B", // Shade Grey
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
          light: "#6E76FA", // Bright Cobalt
          medium: "#4928FF", // Medium Cobalt
          dark: "#2B0B9E", // Dark Cobalt
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
          DEFAULT: "#FFFFFF", // White
          foreground: "#232326", // Dark Grey
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
          DEFAULT: "#FFFFFF", // White
          foreground: "#232326", // Dark Grey
        },
        // Primary Colors
        primary: {
          DEFAULT: "#00E0B5", // Medium Teal as primary
          light: "#00FFCC", // Bright Teal
          medium: "#00E0B5", // Medium Teal
          shade: "#00A887", // Shade Teal
          dark: "#02786E", // Dark Teal
          foreground: "#FFFFFF",
        },
        // Secondary Colors
        secondary: {
          DEFAULT: "#14E6E6", // Medium Cerulean
          light: "#82F5F5", // Bright Cerulean
          medium: "#14E6E6", // Medium Cerulean
          dark: "#008E99", // Dark Cerulean
          foreground: "#232326",
        },
        // Accent Colors (Cobalt)
        accent: {
          DEFAULT: "#4928FF", // Medium Cobalt
          light: "#6E76FA", // Bright Cobalt
          medium: "#4928FF", // Medium Cobalt
          dark: "#2B0B9E", // Dark Cobalt
          foreground: "#FFFFFF",
        },
        // Neutral Colors
        background: "#FFFFFF", // White
        foreground: "#232326", // Dark Grey
        muted: {
          DEFAULT: "#E4E4E9", // Light Grey
          foreground: "#67676B", // Shade Grey
        },
        card: {
          DEFAULT: "#FFFFFF", // White
          foreground: "#232326", // Dark Grey
        },
        popover: {
          DEFAULT: "#FFFFFF", // White
          foreground: "#232326", // Dark Grey
        },
        border: "#E4E4E9", // Light Grey
        input: "#E4E4E9", // Light Grey
        ring: "#00E0B5", // Medium Teal

        // Semantic Colors
        destructive: {
          DEFAULT: "#FF3B30",
          foreground: "#FFFFFF",
        },
        success: {
          DEFAULT: "#00A887", // Shade Teal
          foreground: "#FFFFFF",
        },
        warning: {
          DEFAULT: "#FF9500",
          foreground: "#FFFFFF",
        },
        info: {
          DEFAULT: "#4928FF", // Medium Cobalt
          foreground: "#FFFFFF",
        },

        // Neutral Color Scale
        neutral: {
          "100": "#FFFFFF", // White
          "200": "#E4E4E9", // Light Grey
          "300": "#A5A5A9", // Medium Grey
          "400": "#67676B", // Shade Grey
          "500": "#232326", // Dark Grey
        },

        // Chart Colors
        chart: {
          "1": "#00E0B5", // Medium Teal
          "2": "#14E6E6", // Medium Cerulean
          "3": "#4928FF", // Medium Cobalt
          "4": "#6E76FA", // Bright Cobalt
          "5": "#00FFCC", // Bright Teal
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
        lg: "0.5rem",
        md: "0.375rem",
        sm: "0.25rem",
      },
      fontFamily: {
        "henderson-mod": ["BCG Henderson Mod", "sans-serif"],
        "henderson-sans": ["BCG Henderson Sans", "sans-serif"],
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
        "subtle-bounce": {
          "0%, 100%": {
            transform: "translateY(0)",
          },
          "50%": {
            transform: "translateY(-2px)",
          },
        },
        "slide-up": {
          "0%": {
            transform: "translateY(100%)",
            opacity: "0",
          },
          "100%": {
            transform: "translateY(0)",
            opacity: "1",
          },
        },
        "slide-down": {
          "0%": {
            transform: "translateY(0)",
            opacity: "1",
          },
          "100%": {
            transform: "translateY(100%)",
            opacity: "0",
          },
        },
        "expand-height": {
          "0%": {
            height: "48px",
            opacity: "0.5",
          },
          "100%": {
            height: "60vh",
            opacity: "1",
          },
        },
        "collapse-height": {
          "0%": {
            height: "60vh",
            opacity: "1",
          },
          "100%": {
            height: "48px",
            opacity: "0.5",
          },
        },
        "zoom-out": {
          "0%": {
            transform: "scale(1)",
            opacity: "1",
          },
          "100%": {
            transform: "scale(1.05)",
            opacity: "0",
          },
        },
        "zoom-in": {
          "0%": {
            transform: "scale(0.95)",
            opacity: "0",
          },
          "100%": {
            transform: "scale(1)",
            opacity: "1",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "subtle-bounce": "subtle-bounce 2s ease-in-out infinite",
        "slide-up": "slide-up 0.3s ease-out",
        "slide-down": "slide-down 0.3s ease-out",
        "expand-height": "expand-height 0.3s ease-out forwards",
        "collapse-height": "collapse-height 0.3s ease-out forwards",
        "zoom-out": "zoom-out 0.3s ease-out forwards",
        "zoom-in": "zoom-in 0.3s ease-out forwards",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}
export default config
