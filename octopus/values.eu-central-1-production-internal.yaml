global:
  replicaCount: 2
  image:
    tag: #{artifact_version_latest}

  labels:
    environment: "preproduction"
    clustername: "internal-production"

  configmap:
    APP_ENVIRONMENT: PreProduction
    DD_ENV: internal-production
    DD_SERVICE: tech-cost-takeout-app
    DD_VERSION: #{artifact_version_latest}

  serviceAccount:
    create: true
    annotations:
      eks.amazonaws.com/role-arn: "arn:aws:iam::************:role/smp/production/k8s-bcg-na-tech-cost-takeout-services-role-production"

  ingress:
    enabled: true
    hosts:
      - host: tech-cost-takeout-app.internal.production.smp.bcg.com
        paths:
          - path: "/"
            pathType: "Prefix"
    annotations:
      kubernetes.io/ingress.class: "alb"
      alb.ingress.kubernetes.io/ssl-redirect: "443"
      alb.ingress.kubernetes.io/group.name: "bcgx"
      alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
      alb.ingress.kubernetes.io/backend-protocol: HTTP
      alb.ingress.kubernetes.io/success-codes: 200-499
      alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:eu-central-1:************:certificate/f84cc6af-b8a7-4d9e-bc68-14ca8620b311"
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
      alb.ingress.kubernetes.io/scheme: internal
      alb.ingress.kubernetes.io/ssl-policy: ELBSecurityPolicy-TLS13-1-2-2021-06
      alb.ingress.kubernetes.io/target-group-attributes: stickiness.enabled=true,stickiness.lb_cookie.duration_seconds=3600
      alb.ingress.kubernetes.io/wafv2-acl-arn: "arn:aws:wafv2:eu-central-1:************:regional/webacl/smp-regional-prod-waf-v2/ca8d27ce-96b9-4361-850d-cdd78f004b11"
