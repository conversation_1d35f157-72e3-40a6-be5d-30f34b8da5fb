global:
  replicaCount: 2
  image:
    tag: #{artifact_version_latest}

  labels:
    environment: "integration"
    clustername: "smp-core-infra-integration-instance"

  configmap:
    APP_ENVIRONMENT: Integration
    DD_ENV: smp-core-infra-integration-instance
    DD_SERVICE: tech-cost-takeout-app
    DD_VERSION: #{artifact_version_latest}
    OKTA_CLIENT_ID: 0oa2dbrlo5nVxTSJO0h8
    OKTA_ISSUER: https://bcgdev-admin.oktapreview.com/oauth2/aus2dbrlgztMLPWn10h8
    NEXTAUTH_URL: https://tech-cost-takeout-app.internal.integration.smp.bcg.com
  
  containers:
    env:
      - name: NEXT_PUBLIC_API_URL
        value: https://api-euc-internal.integration.smp.bcg.com/tech-cost-takeout-api/api/
      - name: NEXT_PUBLIC_BACKEND_API_KEY
        value: AskQliXde7Y5q2lPbMvZSmZS8WNtxEej

  serviceAccount:
    create: true
    annotations:
      eks.amazonaws.com/role-arn: "arn:aws:iam::************:role/smp/integration/k8s-bcg-na-tech-cost-takeout-services-role-integration"

  ingress:
    enabled: true
    hosts:
      - host: tech-cost-takeout-app.internal.integration.smp.bcg.com
        paths:
          - path: "/"
            pathType: "Prefix"
    annotations:
      kubernetes.io/ingress.class: "alb"
      alb.ingress.kubernetes.io/group.name: "default-internal-1"
      alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
      alb.ingress.kubernetes.io/backend-protocol: HTTP
      alb.ingress.kubernetes.io/success-codes: 200-499
      alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:eu-central-1:************:certificate/9ab6d7f1-cf96-4303-b81a-ce482a51e531"
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
      alb.ingress.kubernetes.io/scheme: internal
      alb.ingress.kubernetes.io/ssl-policy: ELBSecurityPolicy-TLS13-1-2-2021-06
      alb.ingress.kubernetes.io/target-group-attributes: stickiness.enabled=true,stickiness.lb_cookie.duration_seconds=3600
      alb.ingress.kubernetes.io/wafv2-acl-arn: "arn:aws:wafv2:eu-central-1:************:regional/webacl/smp-regional-integration-waf-v2/2247ebe8-1244-4b96-8da5-c59180f01e2d"

  secrets:
    create: true
    provider:
      vault:
        server: "https://dev-us-east-1-vault-cluster.private.vault.6135f521-1d5d-4236-9563-bf788495cc90.aws.hashicorp.cloud:8200"
        namespace: "admin/BCG/SMP/"
        vaultRole: "k8s-bcg-na-tech-cost-takeout-services-role-integration"
        path: "secret"
        version: "v2"
    data:
      - key: "tech-cost-takeout/okta-auth"
        secretKey: "OKTA_CLIENT_SECRET"
        property: "OKTA_CLIENT_SECRET"
      - key: "tech-cost-takeout/okta-auth"
        secretKey: "NEXTAUTH_SECRET"
        property: "NEXTAUTH_SECRET"