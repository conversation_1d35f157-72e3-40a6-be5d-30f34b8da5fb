// src/constants/roles.ts

export const ROLE_ADMIN = 'GenAICostTakeOUT_ADMIN';
export const ROLE_TRANSPARENCY = 'GenAICostTakeOUT_Transparency';
export const ROLE_BENCHMARKING = 'GenAICostTakeOUT_Benchmarking';
export const ROLE_RECOMMENDATIONS = 'GenAICostTakeOUT_Recommendations';

/**
 * Helpers to check for each permission.
 */
export const canViewAdmin = (roles: string[]) =>
  roles.includes(ROLE_ADMIN);

export const canViewTransparency = (roles: string[]) =>
  roles.includes(ROLE_TRANSPARENCY);

export const canViewBenchmarking = (roles: string[]) =>
  roles.includes(ROLE_BENCHMARKING);

export const canViewRecommendations = (roles: string[]) =>
  roles.includes(ROLE_RECOMMENDATIONS);