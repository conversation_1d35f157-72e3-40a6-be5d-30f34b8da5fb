import { File, FileText, FileSpreadsheet, FileArchive } from 'lucide-react'

export const ALLOWED_FILE_EXTENSIONS = [
  '.pdf', '.docx', '.xlsx', '.xls', '.doc', '.ppt', '.pptx',
] as const
export type FileExtension = typeof ALLOWED_FILE_EXTENSIONS[number]

// Metadata for each extension: icon component + Tailwind color class
export interface ExtensionMeta {
  Icon: React.ComponentType<any>
  colorClass: string
}

export const EXTENSION_META_MAP = new Map<FileExtension, ExtensionMeta>([
  ['.pdf',  { Icon: FileText,      colorClass: 'text-red-500'   }],
  ['.doc',  { Icon: FileText,      colorClass: 'text-blue-500'  }],
  ['.docx', { Icon: FileText,      colorClass: 'text-blue-500'  }],
  ['.xls',  { Icon: FileSpreadsheet, colorClass: 'text-green-500' }],
  ['.xlsx', { Icon: FileSpreadsheet, colorClass: 'text-green-500' }],
  ['.ppt',  { Icon: FileArchive,    colorClass: 'text-orange-500'}],
  ['.pptx', { Icon: FileArchive,    colorClass: 'text-orange-500'}],
])