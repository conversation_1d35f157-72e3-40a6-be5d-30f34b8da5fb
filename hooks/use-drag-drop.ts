import { useState, useCallback } from 'react';
import {
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  useSensor,
  useSensors,
  PointerSensor,
  KeyboardSensor,
} from '@dnd-kit/core';
import { DragItem, DropTarget, DragState, Provider, UnmappedDocument, DragDropType } from '@/utils/drag-drop-types';
import { DragDropService } from '@/utils/drag-drop-service';

export interface UseDragDropProps {
  providers: Provider[];
  unmappedDocuments: UnmappedDocument[];
  onDataUpdate: (providers: Provider[], unmappedDocuments: UnmappedDocument[]) => void;
  onError?: (error: string) => void;
}

// Helper function to find the actual item data for drag preview
const findItemData = (dragItem: DragItem, providers: Provider[], unmappedDocuments: UnmappedDocument[]) => {
  switch (dragItem.type) {
    case DragDropType.Unmapped:
      return unmappedDocuments.find(doc => doc.id === dragItem.id);

    case DragDropType.Attachment:
      for (const provider of providers) {
        for (const contract of provider.contracts) {
          const attachment = contract.attachments.find(att => att.id === dragItem.id);
          if (attachment) {
            return { ...attachment, contractId: contract.id, contractName: contract.name };
          }
        }
      }
      return null;

    case DragDropType.Contract:
      for (const provider of providers) {
        const contract = provider.contracts.find(cont => cont.id === dragItem.id);
        if (contract) {
          return { ...contract, providerName: provider.name };
        }
      }
      return null;

    default:
      return null;
  }
};

export const useDragDrop = ({
  providers,
  unmappedDocuments,
  onDataUpdate,
  onError,
}: UseDragDropProps) => {
  const [isDragging, setIsDragging] = useState<string | null>(null);
  const [dragOverTarget, setDragOverTarget] = useState<string | null>(null);
  const [isAnyDragging, setIsAnyDragging] = useState(false);
  const [activeDragItem, setActiveDragItem] = useState<DragItem | null>(null);
  const [draggedItemData, setDraggedItemData] = useState<any>(null);

  // Configure sensors for @dnd-kit
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor)
  );

  const resetDragState = useCallback(() => {
    setIsDragging(null);
    setDragOverTarget(null);
    setIsAnyDragging(false);
    setActiveDragItem(null);
    setDraggedItemData(null);
  }, []);

  // @dnd-kit event handlers
  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event;
    const dragData = active.data.current as DragItem;

    setIsDragging(active.id as string);
    setIsAnyDragging(true);
    setActiveDragItem(dragData);

    // Find the actual item data for preview
    const itemData = findItemData(dragData, providers, unmappedDocuments);
    setDraggedItemData(itemData);
  }, [providers, unmappedDocuments]);

  const handleDragOver = useCallback((event: DragOverEvent) => {
    const { over } = event;
    if (over) {
      setDragOverTarget(over.id as string);
    } else {
      setDragOverTarget(null);
    }
  }, []);

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { over } = event;

    if (!over || !activeDragItem) {
      resetDragState();
      return;
    }

    const sourceParams = activeDragItem;
    const targetParams = over.data.current as DropTarget;

    if (sourceParams.id === targetParams.id) {
      resetDragState();
      return;
    }

    const result = DragDropService.executeDrop(
      sourceParams,
      targetParams,
      providers,
      unmappedDocuments
    );

    if (result.success && result.updatedProviders && result.updatedUnmappedDocuments) {
      onDataUpdate(result.updatedProviders, result.updatedUnmappedDocuments);
    } else if (result.error) {
      onError?.(result.error);
    }

    resetDragState();
  }, [activeDragItem, providers, unmappedDocuments, onDataUpdate, onError, resetDragState]);

  const getDropPreview = useCallback((targetParams: DropTarget) => {
    if (!activeDragItem) {
      return { canDrop: false, message: "No item being dragged" };
    }

    return DragDropService.getDropPreview(activeDragItem, targetParams, providers);
  }, [activeDragItem, providers]);

  const isItemDragging = useCallback((itemId: string) => {
    return isDragging === itemId;
  }, [isDragging]);

  const isTargetDraggedOver = useCallback((targetId: string) => {
    return dragOverTarget === targetId;
  }, [dragOverTarget]);

  const getDragState = useCallback((): DragState => {
    return {
      isDragging,
      dragOverTarget,
      isAnyDragging,
      dragItem: activeDragItem,
      dragNode: null, // Not needed with @dnd-kit
    };
  }, [isDragging, dragOverTarget, isAnyDragging, activeDragItem]);

  return {
    // State
    isDragging,
    dragOverTarget,
    isAnyDragging,
    draggedItemData,
    sensors,

    // @dnd-kit event handlers
    handleDragStart,
    handleDragOver,
    handleDragEnd,

    // Utility functions
    resetDragState,
    getDropPreview,
    isItemDragging,
    isTargetDraggedOver,
    getDragState,
  };
};
